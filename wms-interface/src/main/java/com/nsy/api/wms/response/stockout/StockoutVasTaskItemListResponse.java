package com.nsy.api.wms.response.stockout;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "StockoutVasTaskItemListResponse", description = "增值任务明细列表Response")
public class StockoutVasTaskItemListResponse {

    @ApiModelProperty(value = "id", name = "id")
    private Integer id;
    @ApiModelProperty(value = "规格编码", name = "sku")
    private String sku;
    @ApiModelProperty(value = "数量", name = "vasQty")
    private Integer vasQty;
    @ApiModelProperty(value = "增值类型", name = "vasTypeStr")
    private String vasTypeStr;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getVasQty() {
        return vasQty;
    }

    public void setVasQty(Integer vasQty) {
        this.vasQty = vasQty;
    }

    public String getVasTypeStr() {
        return vasTypeStr;
    }

    public void setVasTypeStr(String vasTypeStr) {
        this.vasTypeStr = vasTypeStr;
    }
}
