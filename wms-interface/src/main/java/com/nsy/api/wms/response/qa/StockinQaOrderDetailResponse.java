
package com.nsy.api.wms.response.qa;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.wms.domain.stockin.QcInboundsPriceItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: caishaohui
 * @time: 2024/12/9 9:47
 */
@ApiModel(value = "StockinQaOrderDetailResponse", description = "入库质检单详情Response")
public class StockinQaOrderDetailResponse {

    /**
     * stockinQaOrderId
     */
    @ApiModelProperty("质检单Id")
    private Integer stockinQaOrderId;

    @ApiModelProperty("商品ID")
    private Integer productId;
    /**
     * 内部箱号
     */
    @ApiModelProperty("内部箱号")
    private String internalBoxCode;
    /**
     * skc
     */
    @ApiModelProperty("颜色编码")
    private String skc;
    /**
     * 规格编码
     */
    @ApiModelProperty("规格编码")
    private String sku;

    /**
     * 商品图片地址
     */
    @ApiModelProperty(value = "商品图片地址", name = "imageUrl")
    private String imageUrl;
    /**
     * 缩略图
     */
    @ApiModelProperty(value = "缩略图", name = "thumbnailImageUrl")
    private String thumbnailImageUrl;

    /**
     * 预览图地址
     */
    @ApiModelProperty(value = "预览图地址", name = "previewImageUrl")
    private String previewImageUrl;

    @ApiModelProperty("到货件数")
    private Integer arrivalCount;

    @ApiModelProperty("箱内数量")
    private Integer boxQty;

    @ApiModelProperty("要求质检数量")
    private Integer qaQty;

    /**
     * 质检数量
     */
    @ApiModelProperty("质检数量")
    private Integer testTotalCount;
    /**
     * 质检不合格件数
     */
    @ApiModelProperty("质检不合格件数")
    private Integer unqualifiedCount;

    @ApiModelProperty("直接退货数")
    private Integer directReturnCount;

    /**
     * 退货数量
     */
    @ApiModelProperty("退货数量")
    private Integer returnCount;
    /**
     * 让步接收数
     */
    @ApiModelProperty("让步接收数")
    private Integer concessionsCount;

    /**
     * 轻微缺陷数量
     */
    @ApiModelProperty("轻微缺陷数量")
    private Integer minorDefectCount;

    /**
     * 严重缺陷数量
     */
    @ApiModelProperty("严重缺陷数量")
    private Integer majorDefectCount;

    /**
     * 
     */
    @ApiModelProperty("致命缺陷数量")
    private Integer criticalDefectCount;

    /**
     * 不合格原因归类
     */
    @ApiModelProperty("不合格原因归类")
    private String unqualifiedCategory;

    @ApiModelProperty("不合格原因")
    private String unqualifiedReason;

    /**
     * 质检不合格的问题描述
     */
    @ApiModelProperty("不合格的问题描述")
    private String unqualifiedQuestion;

    /**
     * 不合格的问题描述
     */
    @ApiModelProperty("次要不合格的问题描述")
    private String unqualifiedQuestionSecondary;

    /**
     * 质检不合格的原因
     */
    @ApiModelProperty("次要不合格原因归类")
    private String unqualifiedCategorySecondary;

    @ApiModelProperty("次要不合格原因")
    private String unqualifiedReasonSecondary;

    /**
     * 供应商名称
     */
    @ApiModelProperty("供应商名称")
    private String supplierName;

    private String processStatus;

    @ApiModelProperty("质检流程状态")
    private String processStatusStr;

    private String result;

    @ApiModelProperty("质检结果")
    private String resultStr;

    @ApiModelProperty("质检人员")
    private String qcUserName;

    @ApiModelProperty("采购员")
    private String purchaseUserName;
    /**
     * 入库时间
     */
    @ApiModelProperty(value = "质检开始时间", name = "qcStartDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date qcStartDate;

    @ApiModelProperty(value = "质检完成时间", name = "qcCompleteDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date qcCompleteDate;

    @ApiModelProperty(value = "工厂发货日期", name = "qcStartDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date deliveryDate;

    @ApiModelProperty(value = "入库日期", name = "stockinDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date stockinDate;

    /**
     * 采购单号
     */
    @ApiModelProperty("采购单号")
    private String purchasePlanNo;

    /**
     * 出库箱码
     */
    @ApiModelProperty("接收单号")
    private String supplierDeliveryBoxCode;

    /**
     * 工厂出库单号
     */
    @ApiModelProperty("工厂出库单号")
    private String supplierDeliveryNo;

    /**
     * 责任方
     */
    @ApiModelProperty("责任方")
    private String responsibility;

    //责任部门
    @ApiModelProperty("责任部门")
    private String departResponsibility;
    /**
     * 处理方案
     */
    @ApiModelProperty("处理方案")
    private String processingProgram;

    @ApiModelProperty("采购单备注")
    private String planRemark;

    @ApiModelProperty("让步接收采购价")
    private List<QcInboundsPriceItem> priceItemList;

    @ApiModelProperty(value = "sop流程", name = "processList")
    private List<StockinQaOrderProcess> processList;

    @ApiModelProperty(value = "质检sop结果", name = "sopProcessResult")
    private StockinQaOrderProcess sopProcessResult;

    @ApiModelProperty(value = "初审结果", name = "firstAuditResult")
    private StockinQaOrderProcess firstAuditResult;

    @ApiModelProperty(value = "复审结果", name = "secondAuditResult")
    private StockinQaOrderProcess secondAuditResult;

    @ApiModelProperty(value = "稽查结果", name = "qaInspectResult")
    private StockinQaOrderProcess qaInspectResult;

    @ApiModelProperty(value = "全检结果", name = "qaFullInspectResult")
    private StockinQaOrderProcess qaFullInspectResult;

    public StockinQaOrderProcess getQaInspectResult() {
        return qaInspectResult;
    }

    public void setQaInspectResult(StockinQaOrderProcess qaInspectResult) {
        this.qaInspectResult = qaInspectResult;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public StockinQaOrderProcess getSopProcessResult() {
        return sopProcessResult;
    }

    public void setSopProcessResult(StockinQaOrderProcess sopProcessResult) {
        this.sopProcessResult = sopProcessResult;
    }

    public StockinQaOrderProcess getSecondAuditResult() {
        return secondAuditResult;
    }

    public void setSecondAuditResult(StockinQaOrderProcess secondAuditResult) {
        this.secondAuditResult = secondAuditResult;
    }

    public Date getStockinDate() {
        return stockinDate;
    }

    public void setStockinDate(Date stockinDate) {
        this.stockinDate = stockinDate;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getThumbnailImageUrl() {
        return thumbnailImageUrl;
    }

    public void setThumbnailImageUrl(String thumbnailImageUrl) {
        this.thumbnailImageUrl = thumbnailImageUrl;
    }

    public String getPreviewImageUrl() {
        return previewImageUrl;
    }

    public void setPreviewImageUrl(String previewImageUrl) {
        this.previewImageUrl = previewImageUrl;
    }

    public StockinQaOrderProcess getFirstAuditResult() {
        return firstAuditResult;
    }

    public void setFirstAuditResult(StockinQaOrderProcess firstAuditResult) {
        this.firstAuditResult = firstAuditResult;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public List<StockinQaOrderProcess> getProcessList() {
        return processList;
    }

    public void setProcessList(List<StockinQaOrderProcess> processList) {
        this.processList = processList;
    }

    public List<QcInboundsPriceItem> getPriceItemList() {
        return priceItemList;
    }

    public void setPriceItemList(List<QcInboundsPriceItem> priceItemList) {
        this.priceItemList = priceItemList;
    }

    public String getUnqualifiedQuestionSecondary() {
        return unqualifiedQuestionSecondary;
    }

    public void setUnqualifiedQuestionSecondary(String unqualifiedQuestionSecondary) {
        this.unqualifiedQuestionSecondary = unqualifiedQuestionSecondary;
    }

    public String getUnqualifiedCategorySecondary() {
        return unqualifiedCategorySecondary;
    }

    public void setUnqualifiedCategorySecondary(String unqualifiedCategorySecondary) {
        this.unqualifiedCategorySecondary = unqualifiedCategorySecondary;
    }

    public String getUnqualifiedReasonSecondary() {
        return unqualifiedReasonSecondary;
    }

    public void setUnqualifiedReasonSecondary(String unqualifiedReasonSecondary) {
        this.unqualifiedReasonSecondary = unqualifiedReasonSecondary;
    }

    public Integer getStockinQaOrderId() {
        return stockinQaOrderId;
    }

    public void setStockinQaOrderId(Integer stockinQaOrderId) {
        this.stockinQaOrderId = stockinQaOrderId;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getBoxQty() {
        return boxQty;
    }

    public void setBoxQty(Integer boxQty) {
        this.boxQty = boxQty;
    }

    public Integer getArrivalCount() {
        return arrivalCount;
    }

    public void setArrivalCount(Integer arrivalCount) {
        this.arrivalCount = arrivalCount;
    }

    public Integer getTestTotalCount() {
        return testTotalCount;
    }

    public void setTestTotalCount(Integer testTotalCount) {
        this.testTotalCount = testTotalCount;
    }

    public Integer getUnqualifiedCount() {
        return unqualifiedCount;
    }

    public void setUnqualifiedCount(Integer unqualifiedCount) {
        this.unqualifiedCount = unqualifiedCount;
    }

    public Integer getReturnCount() {
        return returnCount;
    }

    public void setReturnCount(Integer returnCount) {
        this.returnCount = returnCount;
    }

    public Integer getConcessionsCount() {
        return concessionsCount;
    }

    public void setConcessionsCount(Integer concessionsCount) {
        this.concessionsCount = concessionsCount;
    }

    public String getUnqualifiedCategory() {
        return unqualifiedCategory;
    }

    public void setUnqualifiedCategory(String unqualifiedCategory) {
        this.unqualifiedCategory = unqualifiedCategory;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }

    public String getProcessStatusStr() {
        return processStatusStr;
    }

    public void setProcessStatusStr(String processStatusStr) {
        this.processStatusStr = processStatusStr;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getResultStr() {
        return resultStr;
    }

    public void setResultStr(String resultStr) {
        this.resultStr = resultStr;
    }

    public String getQcUserName() {
        return qcUserName;
    }

    public void setQcUserName(String qcUserName) {
        this.qcUserName = qcUserName;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public Integer getQaQty() {
        return qaQty;
    }

    public void setQaQty(Integer qaQty) {
        this.qaQty = qaQty;
    }

    public Integer getDirectReturnCount() {
        return directReturnCount;
    }

    public void setDirectReturnCount(Integer directReturnCount) {
        this.directReturnCount = directReturnCount;
    }

    public String getUnqualifiedReason() {
        return unqualifiedReason;
    }

    public void setUnqualifiedReason(String unqualifiedReason) {
        this.unqualifiedReason = unqualifiedReason;
    }

    public String getUnqualifiedQuestion() {
        return unqualifiedQuestion;
    }

    public void setUnqualifiedQuestion(String unqualifiedQuestion) {
        this.unqualifiedQuestion = unqualifiedQuestion;
    }

    public String getPurchaseUserName() {
        return purchaseUserName;
    }

    public void setPurchaseUserName(String purchaseUserName) {
        this.purchaseUserName = purchaseUserName;
    }

    public Date getQcStartDate() {
        return qcStartDate;
    }

    public void setQcStartDate(Date qcStartDate) {
        this.qcStartDate = qcStartDate;
    }

    public Date getQcCompleteDate() {
        return qcCompleteDate;
    }

    public void setQcCompleteDate(Date qcCompleteDate) {
        this.qcCompleteDate = qcCompleteDate;
    }

    public String getPurchasePlanNo() {
        return purchasePlanNo;
    }

    public void setPurchasePlanNo(String purchasePlanNo) {
        this.purchasePlanNo = purchasePlanNo;
    }

    public String getSupplierDeliveryBoxCode() {
        return supplierDeliveryBoxCode;
    }

    public void setSupplierDeliveryBoxCode(String supplierDeliveryBoxCode) {
        this.supplierDeliveryBoxCode = supplierDeliveryBoxCode;
    }

    public String getResponsibility() {
        return responsibility;
    }

    public void setResponsibility(String responsibility) {
        this.responsibility = responsibility;
    }

    public String getDepartResponsibility() {
        return departResponsibility;
    }

    public void setDepartResponsibility(String departResponsibility) {
        this.departResponsibility = departResponsibility;
    }

    public String getProcessingProgram() {
        return processingProgram;
    }

    public void setProcessingProgram(String processingProgram) {
        this.processingProgram = processingProgram;
    }

    public Integer getMinorDefectCount() {
        return minorDefectCount;
    }

    public void setMinorDefectCount(Integer minorDefectCount) {
        this.minorDefectCount = minorDefectCount;
    }

    public Integer getMajorDefectCount() {
        return majorDefectCount;
    }

    public void setMajorDefectCount(Integer majorDefectCount) {
        this.majorDefectCount = majorDefectCount;
    }

    public Integer getCriticalDefectCount() {
        return criticalDefectCount;
    }

    public void setCriticalDefectCount(Integer criticalDefectCount) {
        this.criticalDefectCount = criticalDefectCount;
    }

    public StockinQaOrderProcess getQaFullInspectResult() {
        return qaFullInspectResult;
    }

    public void setQaFullInspectResult(StockinQaOrderProcess qaFullInspectResult) {
        this.qaFullInspectResult = qaFullInspectResult;
    }

    public String getPlanRemark() {
        return planRemark;
    }

    public void setPlanRemark(String planRemark) {
        this.planRemark = planRemark;
    }
}
