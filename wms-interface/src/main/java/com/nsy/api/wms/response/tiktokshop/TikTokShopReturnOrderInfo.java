package com.nsy.api.wms.response.tiktokshop;

/**
 * TikTok Shop 退货单信息
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
public class TikTokShopReturnOrderInfo {

    /**
     * 退货单编号
     */
    private String code;

    /**
     * 退货单状态
     */
    private String status;

    /**
     * 退货仓库
     */
    private String warehouseCode;

    /**
     * 退货方式
     */
    private String method;

    /**
     * 退货单类型
     */
    private String type;

    /**
     * 退货来源
     */
    private String source;

    /**
     * 退货原因
     */
    private String reasonType;

    /**
     * 退货具体原因
     */
    private String detailedReason;

    /**
     * 关联单号，仓库出库批次编号
     */
    private String outboundBatchCode;

    /**
     * 退货物流信息
     */
    private TikTokShopReturnLogisticsInfo logistics;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getReasonType() {
        return reasonType;
    }

    public void setReasonType(String reasonType) {
        this.reasonType = reasonType;
    }

    public String getDetailedReason() {
        return detailedReason;
    }

    public void setDetailedReason(String detailedReason) {
        this.detailedReason = detailedReason;
    }

    public String getOutboundBatchCode() {
        return outboundBatchCode;
    }

    public void setOutboundBatchCode(String outboundBatchCode) {
        this.outboundBatchCode = outboundBatchCode;
    }

    public TikTokShopReturnLogisticsInfo getLogistics() {
        return logistics;
    }

    public void setLogistics(TikTokShopReturnLogisticsInfo logistics) {
        this.logistics = logistics;
    }
} 