package com.nsy.api.wms.response.tiktokshop;

import java.util.List;

/**
 * TikTok Shop 退货单列表查询响应
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
public class TikTokShopReturnOrderSearchResponse {

    /**
     * 下一页token
     */
    private String nextPageToken;

    /**
     * 总记录数
     */
    private Integer totalCount;

    /**
     * 退货单列表
     */
    private List<TikTokShopReturnOrderInfo> returnOrders;

    public String getNextPageToken() {
        return nextPageToken;
    }

    public void setNextPageToken(String nextPageToken) {
        this.nextPageToken = nextPageToken;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public List<TikTokShopReturnOrderInfo> getReturnOrders() {
        return returnOrders;
    }

    public void setReturnOrders(List<TikTokShopReturnOrderInfo> returnOrders) {
        this.returnOrders = returnOrders;
    }
} 