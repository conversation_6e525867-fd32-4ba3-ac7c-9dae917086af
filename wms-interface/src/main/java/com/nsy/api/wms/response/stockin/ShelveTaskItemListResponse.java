package com.nsy.api.wms.response.stockin;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "ShelveTaskItemListResponse", description = "上架任务明细response")
public class ShelveTaskItemListResponse {

    @ApiModelProperty(value = "上架任务明细id", name = "shelveTaskItemId")
    private Integer shelveTaskItemId;

    @ApiModelProperty(value = "图片", name = "thumbnailImageUrl")
    private String thumbnailImageUrl;

    @ApiModelProperty(value = "预览图片", name = "previewImageUrl")
    private String previewImageUrl;

    @ApiModelProperty(value = "商品编码", name = "spu")
    private String spu;

    @ApiModelProperty(value = "规格编码", name = "sku")
    private String sku;

    /**
     * 工厂出库单号
     */
    private String supplierDeliveryNo;

    @ApiModelProperty(value = "颜色", name = "color")
    private String color;

    @ApiModelProperty(value = "尺寸", name = "size")
    private String size;

    @ApiModelProperty(value = "已入库数", name = "stockinQty")
    private Integer stockinQty;

    @ApiModelProperty(value = "退货数", name = "returnedQty")
    private Integer returnedQty;

    @ApiModelProperty(value = "待上架数", name = "pendingQty")
    private Integer pendingQty;

    @ApiModelProperty(value = "已上架数", name = "shelvedQty")
    private Integer shelvedQty;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @ApiModelProperty(value = "库位", name = "positionCode")
    private String positionCode;

    @ApiModelProperty(value = "当前库存", name = "stock")
    private Integer stock;

    @ApiModelProperty(value = "上架人", name = "operator")
    private String operator;

    @ApiModelProperty(value = "上架时间", name = "operateStartDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operatorDate;

    @ApiModelProperty(value = "入库时间", name = "stockinDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stockinDate;

    @ApiModelProperty(value = "不合格的原因归类", name = "unqualifiedCategory")
    private String unqualifiedCategory;

    @ApiModelProperty(value = "是否快进快出", name = "isFbaQuick")
    private Integer isFbaQuick;

    @ApiModelProperty(value = "品牌名称", name = "brandName")
    private String brandName;

    @ApiModelProperty(value = "是否绿色通道", name = "isGreenChannel")
    private Boolean isGreenChannel = Boolean.FALSE;

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public Integer getIsFbaQuick() {
        return isFbaQuick;
    }

    public void setIsFbaQuick(Integer isFbaQuick) {
        this.isFbaQuick = isFbaQuick;
    }

    public Integer getShelveTaskItemId() {
        return shelveTaskItemId;
    }

    public void setShelveTaskItemId(Integer shelveTaskItemId) {
        this.shelveTaskItemId = shelveTaskItemId;
    }

    public String getThumbnailImageUrl() {
        return thumbnailImageUrl;
    }

    public void setThumbnailImageUrl(String thumbnailImageUrl) {
        this.thumbnailImageUrl = thumbnailImageUrl;
    }

    public String getPreviewImageUrl() {
        return previewImageUrl;
    }

    public void setPreviewImageUrl(String previewImageUrl) {
        this.previewImageUrl = previewImageUrl;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getStockinQty() {
        return stockinQty;
    }

    public void setStockinQty(Integer stockinQty) {
        this.stockinQty = stockinQty;
    }

    public Integer getReturnedQty() {
        return returnedQty;
    }

    public void setReturnedQty(Integer returnedQty) {
        this.returnedQty = returnedQty;
    }

    public Integer getPendingQty() {
        return pendingQty;
    }

    public void setPendingQty(Integer pendingQty) {
        this.pendingQty = pendingQty;
    }

    public Integer getShelvedQty() {
        return shelvedQty;
    }

    public void setShelvedQty(Integer shelvedQty) {
        this.shelvedQty = shelvedQty;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }


    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public Date getOperatorDate() {
        return operatorDate;
    }

    public void setOperatorDate(Date operatorDate) {
        this.operatorDate = operatorDate;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public Date getStockinDate() {
        return stockinDate;
    }

    public void setStockinDate(Date stockinDate) {
        this.stockinDate = stockinDate;
    }

    public String getUnqualifiedCategory() {
        return unqualifiedCategory;
    }

    public void setUnqualifiedCategory(String unqualifiedCategory) {
        this.unqualifiedCategory = unqualifiedCategory;
    }


    public Boolean getIsGreenChannel() {
        return isGreenChannel;
    }

    public void setIsGreenChannel(Boolean isGreenChannel) {
        this.isGreenChannel = isGreenChannel;
    }
}
