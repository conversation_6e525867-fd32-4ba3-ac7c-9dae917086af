package com.nsy.api.wms.domain.stockin;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 */

public class StockinSupplierDeliveryOrderSpaceAndStockinQtyDTO {


    private String supplierDeliveryNo;


    private Integer stockinQty;


    private Integer expectedQty;


    private Integer spaceId;


    private String spaceName;

    private Integer isFbaQuick;

    private String sku;

    private String skc;

    private String size;

    private Integer boxNum;

    private String purchasePlanNo;

    @ApiModelProperty(value = "收货完成时间", name = "operateEndDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateEndDate;

    @ApiModelProperty(value = "标签属性：S、A、新品首单、改版首单、正常、AMZN新", name = "labelAttributeNames")
    private String labelAttributeNames;

    public Date getOperateEndDate() {
        return operateEndDate;
    }

    public void setOperateEndDate(Date operateEndDate) {
        this.operateEndDate = operateEndDate;
    }

    public Integer getIsFbaQuick() {
        return isFbaQuick;
    }

    public void setIsFbaQuick(Integer isFbaQuick) {
        this.isFbaQuick = isFbaQuick;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public Integer getStockinQty() {
        return stockinQty;
    }

    public void setStockinQty(Integer stockinQty) {
        this.stockinQty = stockinQty;
    }

    public Integer getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(Integer expectedQty) {
        this.expectedQty = expectedQty;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public Integer getBoxNum() {
        return boxNum;
    }

    public void setBoxNum(Integer boxNum) {
        this.boxNum = boxNum;
    }

    public String getPurchasePlanNo() {
        return purchasePlanNo;
    }

    public void setPurchasePlanNo(String purchasePlanNo) {
        this.purchasePlanNo = purchasePlanNo;
    }

    public String getLabelAttributeNames() {
        return labelAttributeNames;
    }

    public void setLabelAttributeNames(String labelAttributeNames) {
        this.labelAttributeNames = labelAttributeNames;
    }
}
