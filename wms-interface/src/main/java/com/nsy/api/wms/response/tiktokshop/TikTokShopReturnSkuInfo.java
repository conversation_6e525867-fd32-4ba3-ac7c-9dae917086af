package com.nsy.api.wms.response.tiktokshop;

/**
 * TikTok Shop 退货单 SKU 信息
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
public class TikTokShopReturnSkuInfo {

    /**
     * 平台SKU编码
     */
    private String platformSkuCode;

    /**
     * SKU图片
     */
    private String imageUrl;

    /**
     * 平台SKU商品条码
     */
    private String barcode;

    /**
     * 商家SKU货号
     */
    private String externalSkuCode;

    /**
     * 商家SKC货号
     */
    private String externalSkcCode;

    /**
     * SKC关键属性一属性英文名
     */
    private String firstKeyAttributeNameEn;

    /**
     * SKC关键属性一属性中文名
     */
    private String firstKeyAttributeNameZh;

    /**
     * SKC关键属性一属性英文值
     */
    private String firstKeyAttributeValueEn;

    /**
     * SKC关键属性一属性中文值
     */
    private String firstKeyAttributeValueZh;

    /**
     * SKU关键属性二属性英文名
     */
    private String secondKeyAttributeNameEn;

    /**
     * SKU关键属性二属性中文名
     */
    private String secondKeyAttributeNameZh;

    /**
     * SKU关键属性二属性英文值
     */
    private String secondKeyAttributeValueEn;

    /**
     * SKU关键属性二属性中文值
     */
    private String secondKeyAttributeValueZh;

    /**
     * SKU申请退货数量
     */
    private Integer requestQuantity;

    /**
     * SKU确认退货数量
     */
    private Integer confirmQuantity;

    /**
     * SKU实退数量
     */
    private Integer actualQuantity;

    /**
     * 原因，修改数量原因
     */
    private String confirmReason;

    /**
     * 原因，拦截退货原因
     */
    private String cancelReason;

    public String getPlatformSkuCode() {
        return platformSkuCode;
    }

    public void setPlatformSkuCode(String platformSkuCode) {
        this.platformSkuCode = platformSkuCode;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getExternalSkuCode() {
        return externalSkuCode;
    }

    public void setExternalSkuCode(String externalSkuCode) {
        this.externalSkuCode = externalSkuCode;
    }

    public String getExternalSkcCode() {
        return externalSkcCode;
    }

    public void setExternalSkcCode(String externalSkcCode) {
        this.externalSkcCode = externalSkcCode;
    }

    public String getFirstKeyAttributeNameEn() {
        return firstKeyAttributeNameEn;
    }

    public void setFirstKeyAttributeNameEn(String firstKeyAttributeNameEn) {
        this.firstKeyAttributeNameEn = firstKeyAttributeNameEn;
    }

    public String getFirstKeyAttributeNameZh() {
        return firstKeyAttributeNameZh;
    }

    public void setFirstKeyAttributeNameZh(String firstKeyAttributeNameZh) {
        this.firstKeyAttributeNameZh = firstKeyAttributeNameZh;
    }

    public String getFirstKeyAttributeValueEn() {
        return firstKeyAttributeValueEn;
    }

    public void setFirstKeyAttributeValueEn(String firstKeyAttributeValueEn) {
        this.firstKeyAttributeValueEn = firstKeyAttributeValueEn;
    }

    public String getFirstKeyAttributeValueZh() {
        return firstKeyAttributeValueZh;
    }

    public void setFirstKeyAttributeValueZh(String firstKeyAttributeValueZh) {
        this.firstKeyAttributeValueZh = firstKeyAttributeValueZh;
    }

    public String getSecondKeyAttributeNameEn() {
        return secondKeyAttributeNameEn;
    }

    public void setSecondKeyAttributeNameEn(String secondKeyAttributeNameEn) {
        this.secondKeyAttributeNameEn = secondKeyAttributeNameEn;
    }

    public String getSecondKeyAttributeNameZh() {
        return secondKeyAttributeNameZh;
    }

    public void setSecondKeyAttributeNameZh(String secondKeyAttributeNameZh) {
        this.secondKeyAttributeNameZh = secondKeyAttributeNameZh;
    }

    public String getSecondKeyAttributeValueEn() {
        return secondKeyAttributeValueEn;
    }

    public void setSecondKeyAttributeValueEn(String secondKeyAttributeValueEn) {
        this.secondKeyAttributeValueEn = secondKeyAttributeValueEn;
    }

    public String getSecondKeyAttributeValueZh() {
        return secondKeyAttributeValueZh;
    }

    public void setSecondKeyAttributeValueZh(String secondKeyAttributeValueZh) {
        this.secondKeyAttributeValueZh = secondKeyAttributeValueZh;
    }

    public Integer getRequestQuantity() {
        return requestQuantity;
    }

    public void setRequestQuantity(Integer requestQuantity) {
        this.requestQuantity = requestQuantity;
    }

    public Integer getConfirmQuantity() {
        return confirmQuantity;
    }

    public void setConfirmQuantity(Integer confirmQuantity) {
        this.confirmQuantity = confirmQuantity;
    }

    public Integer getActualQuantity() {
        return actualQuantity;
    }

    public void setActualQuantity(Integer actualQuantity) {
        this.actualQuantity = actualQuantity;
    }

    public String getConfirmReason() {
        return confirmReason;
    }

    public void setConfirmReason(String confirmReason) {
        this.confirmReason = confirmReason;
    }

    public String getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }
} 