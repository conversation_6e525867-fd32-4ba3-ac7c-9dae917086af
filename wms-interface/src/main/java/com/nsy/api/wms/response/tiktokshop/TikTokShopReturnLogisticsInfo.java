package com.nsy.api.wms.response.tiktokshop;

import java.util.List;

/**
 * TikTok Shop 退货单物流信息
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
public class TikTokShopReturnLogisticsInfo {

    /**
     * 物流服务商Code
     */
    private String shippingProviderCode;

    /**
     * 发货批次编号，即物流主单号（仅平台物流包含）
     */
    private String logisticsOrder;

    /**
     * 申请退货数量
     */
    private Integer requestQuantity;

    /**
     * 确认退货数量
     */
    private Integer confirmQuantity;

    /**
     * 实退数量
     */
    private Integer actualQuantity;

    /**
     * 平台SPU编号
     */
    private String platformSpuCode;

    /**
     * 商品类目ID
     */
    private String categoryId;

    /**
     * SKU列表
     */
    private List<TikTokShopReturnSkuInfo> skus;

    public String getShippingProviderCode() {
        return shippingProviderCode;
    }

    public void setShippingProviderCode(String shippingProviderCode) {
        this.shippingProviderCode = shippingProviderCode;
    }

    public String getLogisticsOrder() {
        return logisticsOrder;
    }

    public void setLogisticsOrder(String logisticsOrder) {
        this.logisticsOrder = logisticsOrder;
    }

    public Integer getRequestQuantity() {
        return requestQuantity;
    }

    public void setRequestQuantity(Integer requestQuantity) {
        this.requestQuantity = requestQuantity;
    }

    public Integer getConfirmQuantity() {
        return confirmQuantity;
    }

    public void setConfirmQuantity(Integer confirmQuantity) {
        this.confirmQuantity = confirmQuantity;
    }

    public Integer getActualQuantity() {
        return actualQuantity;
    }

    public void setActualQuantity(Integer actualQuantity) {
        this.actualQuantity = actualQuantity;
    }

    public String getPlatformSpuCode() {
        return platformSpuCode;
    }

    public void setPlatformSpuCode(String platformSpuCode) {
        this.platformSpuCode = platformSpuCode;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public List<TikTokShopReturnSkuInfo> getSkus() {
        return skus;
    }

    public void setSkus(List<TikTokShopReturnSkuInfo> skus) {
        this.skus = skus;
    }
} 