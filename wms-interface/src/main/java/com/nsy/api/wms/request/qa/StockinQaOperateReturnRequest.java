package com.nsy.api.wms.request.qa;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: caishaohui
 * @time: 2024/12/2 14:19
 */
public class StockinQaOperateReturnRequest {

    @ApiModelProperty("质检单ID")
    @NotNull
    private Integer stockinQaOrderId;

    @ApiModelProperty(value = "内部箱号,减少退货数才需要传", name = "internalBoxCode")
    private String internalBoxCode;
    /**
     * 直接退货件数
     */
    @ApiModelProperty("增加/减少退货件数")
    private Integer qty;

    /**
     * 不合格的问题描述
     */
    @ApiModelProperty("不合格的问题描述")
    private String unqualifiedQuestion;

    /**
     * 质检不合格的原因
     */
    @ApiModelProperty("不合格原因归类")
    private String unqualifiedCategory;

    @ApiModelProperty("不合格原因")
    private String unqualifiedReason;

    public String getUnqualifiedQuestion() {
        return unqualifiedQuestion;
    }

    public void setUnqualifiedQuestion(String unqualifiedQuestion) {
        this.unqualifiedQuestion = unqualifiedQuestion;
    }

    public String getUnqualifiedCategory() {
        return unqualifiedCategory;
    }

    public void setUnqualifiedCategory(String unqualifiedCategory) {
        this.unqualifiedCategory = unqualifiedCategory;
    }

    public String getUnqualifiedReason() {
        return unqualifiedReason;
    }

    public void setUnqualifiedReason(String unqualifiedReason) {
        this.unqualifiedReason = unqualifiedReason;
    }

    public Integer getStockinQaOrderId() {
        return stockinQaOrderId;
    }

    public void setStockinQaOrderId(Integer stockinQaOrderId) {
        this.stockinQaOrderId = stockinQaOrderId;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }
}

