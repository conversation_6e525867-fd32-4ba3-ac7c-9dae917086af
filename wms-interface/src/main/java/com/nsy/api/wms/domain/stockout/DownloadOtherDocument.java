package com.nsy.api.wms.domain.stockout;

public class DownloadOtherDocument {
    private String objectName;
    private String baseName;
    private Boolean isLabelPdf;
    private String labelUrl;
    private String fileName;
    private byte[] dataByte;
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public byte[] getDataByte() {
        return dataByte;
    }

    public void setDataByte(byte[] dataByte) {
        this.dataByte = dataByte;
    }

    public Boolean getLabelPdf() {
        return isLabelPdf;
    }

    public String getLabelUrl() {
        return labelUrl;
    }

    public void setLabelUrl(String labelUrl) {
        this.labelUrl = labelUrl;
    }

    public void setLabelPdf(Boolean labelPdf) {
        isLabelPdf = labelPdf;
    }

    public String getObjectName() {
        return objectName;
    }

    public void setObjectName(String objectName) {
        this.objectName = objectName;
    }

    public String getBaseName() {
        return baseName;
    }

    public void setBaseName(String baseName) {
        this.baseName = baseName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
}
