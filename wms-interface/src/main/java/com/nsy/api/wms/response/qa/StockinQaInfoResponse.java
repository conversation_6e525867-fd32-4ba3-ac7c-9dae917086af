package com.nsy.api.wms.response.qa;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nsy.api.wms.domain.product.ProductShopifyMarketing;
import com.nsy.api.wms.domain.qa.AttachmentDto;
import com.nsy.api.wms.domain.qa.BdTagDto;
import com.nsy.api.wms.domain.qa.ProductBomAttachmentDto;
import com.nsy.api.wms.domain.qa.ProductSkcBomMaterialOfferingDto;
import com.nsy.api.wms.domain.qa.StockinQaProductSampleRecordDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description:
 * @author: caishaohui
 * @time: 2024/11/27 14:42
 */
@ApiModel(value = "StockinQaInfoResponse", description = "质检基础数据")
public class StockinQaInfoResponse {

    @ApiModelProperty(value = "入库类型", name = "stockinType")
    private String stockinType;

    @ApiModelProperty(value = "商品基础信息", name = "baseInfo")
    private ProductBaseInfo baseInfo;

    @ApiModelProperty(value = "面辅料明细", name = "productSkcBomMaterialOfferingDtoList")
    private List<ProductSkcBomMaterialOfferingDto> productSkcBomMaterialOfferingDtoList;

    @ApiModelProperty(value = "工艺信息", name = "designInfo")
    private DesignInfo designInfo;

    @ApiModelProperty(value = "产前样质检结果", name = "sampleRecordDto")
    private StockinQaProductSampleRecordDto sampleRecordDto;

    @ApiModelProperty(value = "商品尺寸信息", name = "productShopifyMarketing")
    private ProductShopifyMarketing productShopifyMarketing;

    @ApiModelProperty(value = "是否需要提醒", name = "isNotice 1是 0否")
    private Integer isNotice = 0;

    /**
     * 质检不合格的问题描述
     */
    @ApiModelProperty(value = "前一次质检问题描述", name = "unqualifiedQuestion")
    private String unqualifiedQuestion;
    /**
     * 不合格原因
     */
    @ApiModelProperty(value = "前一次质检问题原因", name = "unqualifiedReason")
    private String unqualifiedReason;

    public String getStockinType() {
        return stockinType;
    }

    public void setStockinType(String stockinType) {
        this.stockinType = stockinType;
    }

    public Integer getIsNotice() {
        return isNotice;
    }

    public ProductShopifyMarketing getProductShopifyMarketing() {
        return productShopifyMarketing;
    }

    public void setProductShopifyMarketing(ProductShopifyMarketing productShopifyMarketing) {
        this.productShopifyMarketing = productShopifyMarketing;
    }

    public void setIsNotice(Integer isNotice) {
        this.isNotice = isNotice;
    }

    public String getUnqualifiedQuestion() {
        return unqualifiedQuestion;
    }

    public void setUnqualifiedQuestion(String unqualifiedQuestion) {
        this.unqualifiedQuestion = unqualifiedQuestion;
    }

    public String getUnqualifiedReason() {
        return unqualifiedReason;
    }

    public void setUnqualifiedReason(String unqualifiedReason) {
        this.unqualifiedReason = unqualifiedReason;
    }

    public StockinQaProductSampleRecordDto getSampleRecordDto() {
        return sampleRecordDto;
    }

    public void setSampleRecordDto(StockinQaProductSampleRecordDto sampleRecordDto) {
        this.sampleRecordDto = sampleRecordDto;
    }

    public List<ProductSkcBomMaterialOfferingDto> getProductSkcBomMaterialOfferingDtoList() {
        return productSkcBomMaterialOfferingDtoList;
    }

    public void setProductSkcBomMaterialOfferingDtoList(List<ProductSkcBomMaterialOfferingDto> productSkcBomMaterialOfferingDtoList) {
        this.productSkcBomMaterialOfferingDtoList = productSkcBomMaterialOfferingDtoList;
    }

    public DesignInfo getDesignInfo() {
        return designInfo;
    }

    public void setDesignInfo(DesignInfo designInfo) {
        this.designInfo = designInfo;
    }

    public ProductBaseInfo getBaseInfo() {
        return baseInfo;
    }

    public void setBaseInfo(ProductBaseInfo baseInfo) {
        this.baseInfo = baseInfo;
    }


    public static class DesignInfo {
        @ApiModelProperty(value = "包装方式", name = "packageName")
        private String packageName;

        @ApiModelProperty(value = "包装袋规格", name = "packageName")
        private String packageSize;

        @ApiModelProperty(value = "成分", name = "fabricType")
        private String fabricType;

        @ApiModelProperty(value = "英文成分", name = "fabricTypeEn")
        private String fabricTypeEn;

        @ApiModelProperty(value = "工艺版本号", name = "workmanshipVersion")
        private String workmanshipVersion;

        @ApiModelProperty(value = "洗水唛模板", name = "washLabel")
        private String washLabel;

        @ApiModelProperty(value = "吊牌信息", name = "bdTagDto")
        private BdTagDto bdTagDto;

        @ApiModelProperty(value = "包装完尺寸", name = "packageSize")
        private String packedSize;

        @ApiModelProperty("重量(kg)")
        private BigDecimal weight;

        @ApiModelProperty("体积重(KG)")
        private BigDecimal volumeWeight;

        @ApiModelProperty("计费重量(KG)")
        private BigDecimal chargedWeight;

        @ApiModelProperty("FBA配送费($)")
        private BigDecimal fbaCost;

        @ApiModelProperty(value = "工艺单信息", name = "productBomAttachmentDtoList")
        private List<ProductBomAttachmentDto> productBomAttachmentDtoList;

        @ApiModelProperty(value = "工艺单PDF附件", name = "workmanshipSheet")
        private AttachmentDto workmanshipSheet;

        @ApiModelProperty(value = "审版意见单", name = "reviewFeedbackAttachList")
        private List<ProductBomAttachmentDto> reviewFeedbackAttachList;

        public String getPackedSize() {
            return packedSize;
        }

        public void setPackedSize(String packedSize) {
            this.packedSize = packedSize;
        }

        public BigDecimal getWeight() {
            return weight;
        }

        public void setWeight(BigDecimal weight) {
            this.weight = weight;
        }

        public BigDecimal getVolumeWeight() {
            return volumeWeight;
        }

        public void setVolumeWeight(BigDecimal volumeWeight) {
            this.volumeWeight = volumeWeight;
        }

        public BigDecimal getChargedWeight() {
            return chargedWeight;
        }

        public void setChargedWeight(BigDecimal chargedWeight) {
            this.chargedWeight = chargedWeight;
        }

        public BigDecimal getFbaCost() {
            return fbaCost;
        }

        public void setFbaCost(BigDecimal fbaCost) {
            this.fbaCost = fbaCost;
        }

        public String getPackageName() {
            return packageName;
        }

        public void setPackageName(String packageName) {
            this.packageName = packageName;
        }

        public String getFabricType() {
            return fabricType;
        }

        public void setFabricType(String fabricType) {
            this.fabricType = fabricType;
        }

        public String getFabricTypeEn() {
            return fabricTypeEn;
        }

        public void setFabricTypeEn(String fabricTypeEn) {
            this.fabricTypeEn = fabricTypeEn;
        }

        public String getWorkmanshipVersion() {
            return workmanshipVersion;
        }

        public void setWorkmanshipVersion(String workmanshipVersion) {
            this.workmanshipVersion = workmanshipVersion;
        }

        public String getWashLabel() {
            return washLabel;
        }

        public void setWashLabel(String washLabel) {
            this.washLabel = washLabel;
        }

        public BdTagDto getBdTagDto() {
            return bdTagDto;
        }

        public void setBdTagDto(BdTagDto bdTagDto) {
            this.bdTagDto = bdTagDto;
        }

        public AttachmentDto getWorkmanshipSheet() {
            return workmanshipSheet;
        }

        public void setWorkmanshipSheet(AttachmentDto workmanshipSheet) {
            this.workmanshipSheet = workmanshipSheet;
        }

        public List<ProductBomAttachmentDto> getProductBomAttachmentDtoList() {
            return productBomAttachmentDtoList;
        }

        public void setProductBomAttachmentDtoList(List<ProductBomAttachmentDto> productBomAttachmentDtoList) {
            this.productBomAttachmentDtoList = productBomAttachmentDtoList;
        }

        public List<ProductBomAttachmentDto> getReviewFeedbackAttachList() {
            return reviewFeedbackAttachList;
        }

        public void setReviewFeedbackAttachList(List<ProductBomAttachmentDto> reviewFeedbackAttachList) {
            this.reviewFeedbackAttachList = reviewFeedbackAttachList;
        }

        public String getPackageSize() {
            return packageSize;
        }

        public void setPackageSize(String packageSize) {
            this.packageSize = packageSize;
        }
    }

    public static class ProductBaseInfo {

        @ApiModelProperty(value = "标签", name = "label")
        private String label;

        @ApiModelProperty(value = "商品Id", name = "productId")
        private Integer productId;

        @ApiModelProperty(value = "规格编码", name = "sku")
        private String sku;

        @JsonProperty("skc")
        private String skc;

        @JsonProperty("spu")
        private String spu;

        @ApiModelProperty(value = "颜色", name = "color")
        private String color;

        @ApiModelProperty(value = "尺码", name = "size")
        private String size;

        @ApiModelProperty(value = "工厂出库单号", name = "supplierDeliveryNo")
        private String supplierDeliveryNo;

        @ApiModelProperty(value = "采购单号", name = "purchasePlanNo")
        private String purchasePlanNo;

        @ApiModelProperty("供应商")
        private String supplierName;

        @ApiModelProperty("内部箱号")
        private String internalBoxCode;

        @ApiModelProperty("到货数量")
        private Integer arrivalCount;

        @ApiModelProperty("箱内数")
        private Integer boxQty;

        @ApiModelProperty("要求质检数量")
        private Integer qaQty;

        @ApiModelProperty("部门")
        private String department;

        @ApiModelProperty("申请部门")
        private String applyDepartment;

        @ApiModelProperty("采购员")
        private String purchaseUserName;

        @ApiModelProperty(value = "品牌名称", name = "brandName")
        private String brandName;


        @ApiModelProperty(value = "是否需要试穿", name = "isTryOn")
        private Boolean isTryOn;

        @ApiModelProperty(value = "是否易撞色", name = "isClash")
        private Integer isClash;

        @ApiModelProperty(value = "包装方式", name = "packageName")
        private String packageName;

        @ApiModelProperty(value = "QC已检标识，1表示有QC已检，0表示没有", name = "isQcChecked")
        private Integer isQcChecked;

        //是否引流款
        private Boolean isLeadGeneration;

        private String planRemark;

        public Integer getBoxQty() {
            return boxQty;
        }

        public void setBoxQty(Integer boxQty) {
            this.boxQty = boxQty;
        }

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public Integer getProductId() {
            return productId;
        }

        public void setProductId(Integer productId) {
            this.productId = productId;
        }

        public String getSupplierName() {
            return supplierName;
        }

        public void setSupplierName(String supplierName) {
            this.supplierName = supplierName;
        }

        public String getSpu() {
            return spu;
        }

        public void setSpu(String spu) {
            this.spu = spu;
        }

        public String getSku() {
            return sku;
        }

        public void setSku(String sku) {
            this.sku = sku;
        }

        public String getSkc() {
            return skc;
        }

        public void setSkc(String skc) {
            this.skc = skc;
        }

        public String getColor() {
            return color;
        }

        public void setColor(String color) {
            this.color = color;
        }

        public String getSize() {
            return size;
        }

        public void setSize(String size) {
            this.size = size;
        }

        public String getSupplierDeliveryNo() {
            return supplierDeliveryNo;
        }

        public void setSupplierDeliveryNo(String supplierDeliveryNo) {
            this.supplierDeliveryNo = supplierDeliveryNo;
        }

        public String getPurchasePlanNo() {
            return purchasePlanNo;
        }

        public void setPurchasePlanNo(String purchasePlanNo) {
            this.purchasePlanNo = purchasePlanNo;
        }

        public String getInternalBoxCode() {
            return internalBoxCode;
        }

        public void setInternalBoxCode(String internalBoxCode) {
            this.internalBoxCode = internalBoxCode;
        }

        public Integer getArrivalCount() {
            return arrivalCount;
        }

        public void setArrivalCount(Integer arrivalCount) {
            this.arrivalCount = arrivalCount;
        }

        public Integer getQaQty() {
            return qaQty;
        }

        public void setQaQty(Integer qaQty) {
            this.qaQty = qaQty;
        }

        public String getDepartment() {
            return department;
        }

        public void setDepartment(String department) {
            this.department = department;
        }

        public String getPurchaseUserName() {
            return purchaseUserName;
        }

        public void setPurchaseUserName(String purchaseUserName) {
            this.purchaseUserName = purchaseUserName;
        }

        public String getBrandName() {
            return brandName;
        }

        public void setBrandName(String brandName) {
            this.brandName = brandName;
        }

        public Boolean getTryOn() {
            return isTryOn;
        }

        public void setTryOn(Boolean tryOn) {
            isTryOn = tryOn;
        }

        public Integer getIsClash() {
            return isClash;
        }

        public void setIsClash(Integer isClash) {
            this.isClash = isClash;
        }

        public String getApplyDepartment() {
            return applyDepartment;
        }

        public void setApplyDepartment(String applyDepartment) {
            this.applyDepartment = applyDepartment;
        }

        public String getPackageName() {
            return packageName;
        }

        public void setPackageName(String packageName) {
            this.packageName = packageName;
        }

        public Boolean getIsLeadGeneration() {
            return isLeadGeneration;
        }

        public void setIsLeadGeneration(Boolean isLeadGeneration) {
            this.isLeadGeneration = isLeadGeneration;
        }

        public String getPlanRemark() {
            return planRemark;
        }

        public void setPlanRemark(String planRemark) {
            this.planRemark = planRemark;
        }

        public Integer getIsQcChecked() {
            return isQcChecked;
        }

        public void setIsQcChecked(Integer isQcChecked) {
            this.isQcChecked = isQcChecked;
        }
    }

}
