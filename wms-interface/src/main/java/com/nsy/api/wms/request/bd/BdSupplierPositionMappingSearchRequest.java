package com.nsy.api.wms.request.bd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "BdSupplierPositionMappingSearchRequest", description = "退货库位查询request")
public class BdSupplierPositionMappingSearchRequest {

    @ApiModelProperty(value = "供应商Id", name = "supplierId")
    private Integer supplierId;

    @ApiModelProperty(value = "是否有原单", name = "hasOriginOrder")
    private Boolean hasOriginOrder;

    @ApiModelProperty(value = "sku或者条码", name = "skuBarcode")
    private String skuBarcode;

    @ApiModelProperty(value = "库位编码或者内部箱子", name = "hasOriginOrder")
    private String positionCode;

    private Integer sourceAreaId;

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public Boolean getHasOriginOrder() {
        return hasOriginOrder;
    }

    public void setHasOriginOrder(Boolean hasOriginOrder) {
        this.hasOriginOrder = hasOriginOrder;
    }

    public String getSkuBarcode() {
        return skuBarcode;
    }

    public void setSkuBarcode(String skuBarcode) {
        this.skuBarcode = skuBarcode;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public Integer getSourceAreaId() {
        return sourceAreaId;
    }

    public void setSourceAreaId(Integer sourceAreaId) {
        this.sourceAreaId = sourceAreaId;
    }
}
