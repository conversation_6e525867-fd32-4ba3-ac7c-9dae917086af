package com.nsy.api.wms.request.qa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-11-13 10:09
 */
@ApiModel(value = "StockinQaStaffConfigSaveRequest", description = "质检人员品类配置保存参数")
public class StockinQaStaffConfigSaveRequest {

    @ApiModelProperty("用户账号")
    @NotBlank(message = "用户账号不能为空")
    private String userCode;

    @ApiModelProperty("仓库")
    @NotNull(message = "仓库不能为空")
    private List<Integer> spaceIdList;

    @ApiModelProperty("当天是否上班 0:否 1:是")
    private Integer isWork;

    @ApiModelProperty("主负责二级分类ID")
    private Integer majorCategoryId;


    @ApiModelProperty("次负责二级分类ID")
    private Integer minorCategoryId;

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public Integer getIsWork() {
        return isWork;
    }

    public void setIsWork(Integer isWork) {
        this.isWork = isWork;
    }

    public Integer getMajorCategoryId() {
        return majorCategoryId;
    }

    public void setMajorCategoryId(Integer majorCategoryId) {
        this.majorCategoryId = majorCategoryId;
    }

    public Integer getMinorCategoryId() {
        return minorCategoryId;
    }

    public void setMinorCategoryId(Integer minorCategoryId) {
        this.minorCategoryId = minorCategoryId;
    }

    public List<Integer> getSpaceIdList() {
        return spaceIdList;
    }

    public void setSpaceIdList(List<Integer> spaceIdList) {
        this.spaceIdList = spaceIdList;
    }
}