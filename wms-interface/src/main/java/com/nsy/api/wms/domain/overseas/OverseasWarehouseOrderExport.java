package com.nsy.api.wms.domain.overseas;

import com.nsy.api.core.apicore.annotation.NsyExcelProperty;

import java.math.BigDecimal;

/**
 * 海外仓订单导出实体
 *
 * <AUTHOR>
 * @since 1.0
 */
public class OverseasWarehouseOrderExport {

    @NsyExcelProperty("出库库单号")
    private String stockoutOrderNo;

    @NsyExcelProperty("订单号")
    private String orderNo;

    @NsyExcelProperty("海外仓订单号")
    private String overseasOrderNo;

    @NsyExcelProperty("店铺")
    private String storeName;

    @NsyExcelProperty("平台")
    private String platformNameCn;

    @NsyExcelProperty("状态")
    private String statusCn;

    @NsyExcelProperty("海外仓")
    private String spaceName;

    @NsyExcelProperty("物流公司")
    private String logisticsCompany;

    @NsyExcelProperty("物流单号")
    private String logisticsNo;

    @NsyExcelProperty("下单时间")
    private String orderTimeStr;

    @NsyExcelProperty("出库单创建时间")
    private String stockoutCreateTimeStr;

    @NsyExcelProperty("推送海外仓时间")
    private String pushOverseasTimeStr;

    @NsyExcelProperty("海外仓发货时间")
    private String overseasShipTimeStr;

    @NsyExcelProperty("包裹提取时间")
    private String packagePickupTimeStr;

    @NsyExcelProperty("包裹签收时间")
    private String packageSignedTimeStr;

    @NsyExcelProperty("推送海外仓时长")
    private BigDecimal pushOverseasDuration;

    @NsyExcelProperty("海外仓处理时长")
    private BigDecimal overseasProcessDuration;

    @NsyExcelProperty("包裹提取时长")
    private BigDecimal packagePickupDuration;

    @NsyExcelProperty("包裹派送时长")
    private BigDecimal packageDeliveryDuration;

    @NsyExcelProperty("发货总时长")
    private BigDecimal totalShipDuration;

    // Getters and Setters

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOverseasOrderNo() {
        return overseasOrderNo;
    }

    public void setOverseasOrderNo(String overseasOrderNo) {
        this.overseasOrderNo = overseasOrderNo;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getPlatformNameCn() {
        return platformNameCn;
    }

    public void setPlatformNameCn(String platformNameCn) {
        this.platformNameCn = platformNameCn;
    }

    public String getStatusCn() {
        return statusCn;
    }

    public void setStatusCn(String statusCn) {
        this.statusCn = statusCn;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getOrderTimeStr() {
        return orderTimeStr;
    }

    public void setOrderTimeStr(String orderTimeStr) {
        this.orderTimeStr = orderTimeStr;
    }

    public String getStockoutCreateTimeStr() {
        return stockoutCreateTimeStr;
    }

    public void setStockoutCreateTimeStr(String stockoutCreateTimeStr) {
        this.stockoutCreateTimeStr = stockoutCreateTimeStr;
    }

    public String getPushOverseasTimeStr() {
        return pushOverseasTimeStr;
    }

    public void setPushOverseasTimeStr(String pushOverseasTimeStr) {
        this.pushOverseasTimeStr = pushOverseasTimeStr;
    }

    public String getOverseasShipTimeStr() {
        return overseasShipTimeStr;
    }

    public void setOverseasShipTimeStr(String overseasShipTimeStr) {
        this.overseasShipTimeStr = overseasShipTimeStr;
    }

    public String getPackagePickupTimeStr() {
        return packagePickupTimeStr;
    }

    public void setPackagePickupTimeStr(String packagePickupTimeStr) {
        this.packagePickupTimeStr = packagePickupTimeStr;
    }

    public String getPackageSignedTimeStr() {
        return packageSignedTimeStr;
    }

    public void setPackageSignedTimeStr(String packageSignedTimeStr) {
        this.packageSignedTimeStr = packageSignedTimeStr;
    }

    public BigDecimal getPushOverseasDuration() {
        return pushOverseasDuration;
    }

    public void setPushOverseasDuration(BigDecimal pushOverseasDuration) {
        this.pushOverseasDuration = pushOverseasDuration;
    }

    public BigDecimal getOverseasProcessDuration() {
        return overseasProcessDuration;
    }

    public void setOverseasProcessDuration(BigDecimal overseasProcessDuration) {
        this.overseasProcessDuration = overseasProcessDuration;
    }

    public BigDecimal getPackagePickupDuration() {
        return packagePickupDuration;
    }

    public void setPackagePickupDuration(BigDecimal packagePickupDuration) {
        this.packagePickupDuration = packagePickupDuration;
    }

    public BigDecimal getPackageDeliveryDuration() {
        return packageDeliveryDuration;
    }

    public void setPackageDeliveryDuration(BigDecimal packageDeliveryDuration) {
        this.packageDeliveryDuration = packageDeliveryDuration;
    }

    public BigDecimal getTotalShipDuration() {
        return totalShipDuration;
    }

    public void setTotalShipDuration(BigDecimal totalShipDuration) {
        this.totalShipDuration = totalShipDuration;
    }
}
