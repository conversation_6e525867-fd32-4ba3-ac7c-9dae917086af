package com.nsy.wms.business.manage.thirdparty;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.enumeration.external.ExternalApiInfoEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiLogStatusEnum;
import com.nsy.api.wms.request.otto.OttoCreateOrderRequest;
import com.nsy.api.wms.request.otto.OttoCreateTmsOrderRequest;
import com.nsy.api.wms.request.otto.OttoGetOrderRequest;
import com.nsy.api.wms.request.otto.OttoGetStockRequest;
import com.nsy.api.wms.request.shein.SheinExpressCompanyListRequest;
import com.nsy.api.wms.request.shein.SheinOrderToShippingRequest;
import com.nsy.api.wms.request.shein.SheinPrintBarcodeRequest;
import com.nsy.api.wms.request.shein.SheinPrintPackageRequest;
import com.nsy.api.wms.request.shein.SheinPurchaseOrderInfosRequest;
import com.nsy.api.wms.request.shein.SheinShippingBasicRequest;
import com.nsy.api.wms.request.stockin.LxAdjustmentOrderRequest;
import com.nsy.api.wms.request.temu.TemuCreateShiporderRequest;
import com.nsy.api.wms.request.temu.TemuCreateShiporderV2Request;
import com.nsy.api.wms.request.temu.TemuGetBoxmarkinfoRequest;
import com.nsy.api.wms.request.temu.TemuGetLabelRequest;
import com.nsy.api.wms.request.temu.TemuGetLogisticsCompanyRequest;
import com.nsy.api.wms.request.temu.TemuGetLogisticsMatchRequest;
import com.nsy.api.wms.request.temu.TemuGetMallAddressRequest;
import com.nsy.api.wms.request.temu.TemuGetPurchaseOrderInfoListRequest;
import com.nsy.api.wms.request.temu.TemuGetReceiveAddressRequest;
import com.nsy.api.wms.request.temu.TemuGetShiporderReceiveaddressRequest;
import com.nsy.api.wms.request.temu.TemuGetShiporderRequest;
import com.nsy.api.wms.request.temu.TemuSendShiporderRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.common.CommonResponse;
import com.nsy.api.wms.response.lx.LxAdjustmentOrderResponse;
import com.nsy.api.wms.response.otto.OttoCreateOrderResponse;
import com.nsy.api.wms.response.otto.OttoCreateTmsOrderResponse;
import com.nsy.api.wms.response.otto.OttoGetOrderResponse;
import com.nsy.api.wms.response.otto.OttoGetStockResponse;
import com.nsy.api.wms.response.shein.SheinExpressCompanyListResponse;
import com.nsy.api.wms.response.shein.SheinOrderToShippingResponse;
import com.nsy.api.wms.response.shein.SheinPrintBarcodeResponse;
import com.nsy.api.wms.response.shein.SheinPrintPackageResponse;
import com.nsy.api.wms.response.shein.SheinPurchaseOrderInfosResponse;
import com.nsy.api.wms.response.shein.SheinResponse;
import com.nsy.api.wms.response.shein.SheinShippingBasicResponse;
import com.nsy.api.wms.response.temu.TemuGetLogisticsMatchResponse;
import com.nsy.api.wms.response.temu.TemuGetPurchaseOrderInfoListResponse;
import com.nsy.api.wms.response.temu.TemuGetReceiveAddressV2Response;
import com.nsy.api.wms.response.temu.TemuLogisticsCompanyGetResponse;
import com.nsy.api.wms.response.temu.TemuMallAddressGetResponse;
import com.nsy.api.wms.response.temu.TemuSendShiporderResponse;
import com.nsy.api.wms.response.temu.TemuShiporderCreateResponse;
import com.nsy.api.wms.response.temu.TemuShiporderGetResponse;
import com.nsy.api.wms.response.temu.TemuShiporderReceiveaddressGetResponse;
import com.nsy.api.wms.response.tiktokdirect.TikTokLocalShippingPrintLabelResponse;
import com.nsy.api.wms.response.tiktokdirect.TikTokLocalShippingShipResponse;
import com.nsy.api.wms.response.tiktokshop.TikTokReserveShipResponse;
import com.nsy.api.wms.response.tiktokshop.TikTokShopCreateOrderResponse;
import com.nsy.api.wms.response.tiktokshop.TikTokShopDeliveryOrderResponse;
import com.nsy.api.wms.response.tiktokshop.TikTokShopPrintResponse;
import com.nsy.api.wms.response.tiktokshop.TikTokShopReturnOrderSearchResponse;
import com.nsy.api.wms.response.tiktokshop.TikTokShopShippingProvidersResponse;
import com.nsy.api.wms.response.tiktokshop.TikTokShopStockUpOrderResponse;
import com.nsy.wms.business.manage.thirdparty.request.tiktokdirect.TikTokLocalShippingPrintLabelRequest;
import com.nsy.wms.business.manage.thirdparty.request.tiktokdirect.TikTokLocalShippingShipRequest;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopCreateOrderRequest;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopDeliveryOrderPrintRequest;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopDeliveryOrderQueryRequest;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopGetLogisticsInfoRequest;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopReserveShipRequest;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopReturnOrderSearchRequest;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopShippingProvidersRequest;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopStockUpOrderQueryRequest;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import com.nsy.wms.repository.entity.external.ExternalApiLogEntity;
import com.nsy.wms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class ThirdPartyApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ThirdPartyApiService.class);

    @Inject
    private RestTemplate restTemplate;
    @Value("${nsy.service.url.thirdparty}")
    private String thirdPartyServiceUrl;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ExternalApiLogService externalApiLogService;

    /**
     * 获取条码
     *
     * @param request
     * @return
     */
    public String getGoodsLabel(TemuGetLabelRequest request) {
        String uri = String.format("%s/platform/temu/get-goods-label", thirdPartyServiceUrl);
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);

        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                CommonResponse<String> commonResponse = objectMapper.readValue(response.getBody(), new TemuGoodsLabelGetResponseTypeReference());
                if (!CommonResponse.PlatformResponseResultEnum.SUCCESS.getValue().equals(commonResponse.getResult())) {
                    throw new BusinessServiceException(commonResponse.getMessage());
                }
                return commonResponse.getData();
            } catch (IOException e) {
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 查询发货快递公司
     *
     * @param request
     * @return
     */
    public TemuLogisticsCompanyGetResponse getLogisticsCompany(TemuGetLogisticsCompanyRequest request) {
        String uri = String.format("%s/platform/temu/get-logistics-company", thirdPartyServiceUrl);
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);

        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                CommonResponse<TemuLogisticsCompanyGetResponse> commonResponse = objectMapper.readValue(response.getBody(), new TemuLogisticsCompanyGetResponseTypeReference());
                if (!CommonResponse.PlatformResponseResultEnum.SUCCESS.getValue().equals(commonResponse.getResult())) {
                    throw new BusinessServiceException(commonResponse.getMessage());
                }
                return commonResponse.getData();
            } catch (IOException e) {
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 查询大仓收货地址
     *
     * @param request
     * @return
     */
    public TemuShiporderReceiveaddressGetResponse getShiporderReceiveaddress(TemuGetShiporderReceiveaddressRequest request) {
        String uri = String.format("%s/platform/temu/get-shiporder-receiveaddress", thirdPartyServiceUrl);
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);

        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                CommonResponse<TemuShiporderReceiveaddressGetResponse> commonResponse = objectMapper.readValue(response.getBody(), new TemuGetShiporderReceiveaddressTypeReference());
                if (!CommonResponse.PlatformResponseResultEnum.SUCCESS.getValue().equals(commonResponse.getResult())) {
                    throw new BusinessServiceException(commonResponse.getMessage());
                }
                return commonResponse.getData();
            } catch (IOException e) {
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }


    /**
     * 获取发货单
     *
     * @param request
     * @return
     */
    public TemuShiporderGetResponse getShiporder(TemuGetShiporderRequest request) {
        String uri = String.format("%s/platform/temu/get-shiporder", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                CommonResponse<TemuShiporderGetResponse> commonResponse = objectMapper.readValue(response.getBody(), new TemuShiporderGetResponseTypeReference());
                LOGGER.info(JsonMapper.toJson(commonResponse));
                if (!CommonResponse.PlatformResponseResultEnum.SUCCESS.getValue().equals(commonResponse.getResult())) {
                    throw new BusinessServiceException(commonResponse.getMessage());
                }
                return commonResponse.getData();
            } catch (IOException e) {
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 获取箱唛信息
     *
     * @param request
     * @return
     */
    public String getBoxmarkinfo(TemuGetBoxmarkinfoRequest request) {
        String uri = String.format("%s/platform/temu/get-boxmarkinfo", thirdPartyServiceUrl);
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);

        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                CommonResponse<String> commonResponse = objectMapper.readValue(response.getBody(), new TemuGetBoxmarkinfoTypeReference());
                if (!CommonResponse.PlatformResponseResultEnum.SUCCESS.getValue().equals(commonResponse.getResult())) {
                    throw new BusinessServiceException(commonResponse.getMessage());
                }
                return commonResponse.getData();
            } catch (IOException e) {
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 操作发货单
     *
     * @param request
     * @return
     */
    public TemuSendShiporderResponse sendShiporder(TemuSendShiporderRequest request) {
        String uri = String.format("%s/platform/temu/send-shiporder", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);

        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.TEMU_SEND_SHIPORDER, uri,
                JsonMapper.toJson(request), request.getDeliveryOrderSnList().get(0), "Temu操作发货单");

        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                CommonResponse<TemuSendShiporderResponse> commonResponse = objectMapper.readValue(response.getBody(), new TemuSendShiporderResponseReference());
                LOGGER.info(JsonMapper.toJson(commonResponse));
                if (!CommonResponse.PlatformResponseResultEnum.SUCCESS.getValue().equals(commonResponse.getResult())) {
                    externalApiLogService.updateLog(apiLogEntity, commonResponse.getMessage(), ExternalApiLogStatusEnum.FAIL);
                    throw new BusinessServiceException(commonResponse.getMessage());
                }
                externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
                return commonResponse.getData();
            } catch (IOException e) {
                externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            externalApiLogService.updateLog(apiLogEntity, "请求失败", ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 获取发货地址
     *
     * @param request
     * @return
     */
    public TemuMallAddressGetResponse getMallAddress(TemuGetMallAddressRequest request) {
        String uri = String.format("%s/platform/temu/get-mall-address", thirdPartyServiceUrl);
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);

        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                CommonResponse<TemuMallAddressGetResponse> commonResponse = objectMapper.readValue(response.getBody(), new TemuMallAddressGetResponseReference());
                if (!CommonResponse.PlatformResponseResultEnum.SUCCESS.getValue().equals(commonResponse.getResult())) {
                    throw new BusinessServiceException(commonResponse.getMessage());
                }
                return commonResponse.getData();
            } catch (IOException e) {
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 获取采购单
     *
     * @param request
     * @return
     */
    public TemuGetPurchaseOrderInfoListResponse getPurchaseOrderList(TemuGetPurchaseOrderInfoListRequest request) {
        String uri = String.format("%s/platform/temu/get-purchase-order-list", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                CommonResponse<TemuGetPurchaseOrderInfoListResponse> commonResponse = objectMapper.readValue(response.getBody(), new TemuGetPurchaseOrderInfoListResponseReference());
                LOGGER.info("获取采购单 {} ", JsonMapper.toJson(commonResponse));
                if (!CommonResponse.PlatformResponseResultEnum.SUCCESS.getValue().equals(commonResponse.getResult())) {
                    throw new BusinessServiceException(commonResponse.getMessage());
                }
                return commonResponse.getData();
            } catch (IOException e) {
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 获取收货仓库V2
     *
     * @param request
     * @return
     */
    public TemuGetReceiveAddressV2Response.Result getShiporderReceiveaddressV2(TemuGetReceiveAddressRequest request) {
        String uri = String.format("%s/platform/temu/get-shiporder-receiveaddress-v2", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                CommonResponse<TemuGetReceiveAddressV2Response.Result> commonResponse = objectMapper.readValue(response.getBody(), new TemuGetReceiveAddressV2ResponseReference());
                LOGGER.info("获取收货仓库V2 {} ", JsonMapper.toJson(commonResponse));
                if (!CommonResponse.PlatformResponseResultEnum.SUCCESS.getValue().equals(commonResponse.getResult())) {
                    throw new BusinessServiceException(commonResponse.getMessage());
                }
                return commonResponse.getData();
            } catch (IOException e) {
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }


    /**
     * 匹配物流
     *
     * @param request
     * @return
     */
    public List<TemuGetLogisticsMatchResponse.ResultItem> getLogisticsMatch(TemuGetLogisticsMatchRequest request) {
        String uri = String.format("%s/platform/temu/get-shiporder-logisticsmatch", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                CommonResponse<List<TemuGetLogisticsMatchResponse.ResultItem>> commonResponse = objectMapper.readValue(response.getBody(), new TemuGetLogisticsMatchResponseReference());
                LOGGER.info("匹配物流 {} ", JsonMapper.toJson(commonResponse));
                if (!CommonResponse.PlatformResponseResultEnum.SUCCESS.getValue().equals(commonResponse.getResult())) {
                    throw new BusinessServiceException(commonResponse.getMessage());
                }
                return commonResponse.getData();
            } catch (IOException e) {
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 创建发货单
     *
     * @param request
     * @return
     */
    public TemuShiporderCreateResponse createShiporder(TemuCreateShiporderRequest request) {
        String uri = String.format("%s/platform/temu/create-shiporder", thirdPartyServiceUrl);
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);

        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.TEMU_CREATE_SHIPORDER, uri,
                JsonMapper.toJson(request), request.getDeliveryOrderCreateGroupList().get(0).getSubWarehouseId().toString(), "Temu创建发货单");

        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                CommonResponse<TemuShiporderCreateResponse> commonResponse = objectMapper.readValue(response.getBody(), new TemuShiporderCreateResponseTypeReference());
                if (!CommonResponse.PlatformResponseResultEnum.SUCCESS.getValue().equals(commonResponse.getResult())) {
                    externalApiLogService.updateLog(apiLogEntity, commonResponse.getMessage(), ExternalApiLogStatusEnum.FAIL);
                    throw new BusinessServiceException(commonResponse.getMessage());
                }
                TemuShiporderCreateResponse data = commonResponse.getData();
                externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(data), ExternalApiLogStatusEnum.SUCCESS);
                return data;
            } catch (IOException e) {
                externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            externalApiLogService.updateLog(apiLogEntity, "请求失败", ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 创建发货单V2
     *
     * @param request
     * @return
     */
    public void createShiporderV2(TemuCreateShiporderV2Request request) {
        String uri = String.format("%s/platform/temu/create-shiporder-v2", thirdPartyServiceUrl);
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.TEMU_CREATE_SHIPORDER, uri,
                JsonMapper.toJson(request), request.getDeliveryOrderCreateGroupList().get(0).getSubWarehouseId().toString(), "Temu创建发货单");
        LOGGER.info(JsonMapper.toJson(request));
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                CommonResponse commonResponse = objectMapper.readValue(response.getBody(), CommonResponse.class);
                LOGGER.info("创建发货单返回 {} ", JsonMapper.toJson(commonResponse));
                if (!CommonResponse.PlatformResponseResultEnum.SUCCESS.getValue().equals(commonResponse.getResult())) {
                    externalApiLogService.updateLog(apiLogEntity, commonResponse.getMessage(), ExternalApiLogStatusEnum.FAIL);
                    throw new BusinessServiceException(String.format("%s %s", commonResponse.getMessage(), request.getDeliveryOrderCreateGroupList().get(0).getThirdPartyDeliveryInfo().getExpressCompanyName()));
                }
                externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
            } catch (IOException e) {
                externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            externalApiLogService.updateLog(apiLogEntity, "请求失败", ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException("请求失败");
        }
    }


    /**
     * 打印条码 shein
     *
     * @param request
     * @return
     */
    public String printBarcodeShein(SheinPrintBarcodeRequest request) {
        String uri = String.format("%s/shein/open-api/goods/print-barcode", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                SheinResponse<SheinPrintBarcodeResponse> sheinResponse = objectMapper.readValue(response.getBody(), new SheinPrintBarcodeResponseTypeReference());
                LOGGER.info(JsonMapper.toJson(sheinResponse));
                if (!"0".equals(sheinResponse.getCode())) {
                    throw new BusinessServiceException(sheinResponse.getMsg());
                }
                if (!CollectionUtils.isEmpty(sheinResponse.getInfo().getErrorData())) {
                    String errorMsg = sheinResponse.getInfo().getErrorData()
                            .stream()
                            .map(errorData -> String.format("【%s】 %s: %s", errorData.getOrderNo(), errorData.getSheinSku(), errorData.getErrorMsg()))
                            .collect(Collectors.joining(";"));
                    throw new BusinessServiceException(errorMsg);
                }
                return sheinResponse.getInfo().getUrl();
            } catch (IOException e) {
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 发货基本信息查询接口 shein
     *
     * @param request
     * @return
     */
    public SheinShippingBasicResponse getShippingBasicShein(SheinShippingBasicRequest request) {
        String uri = String.format("%s/shein/open-api/shipping/basic", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                SheinResponse<SheinShippingBasicResponse> sheinResponse = objectMapper.readValue(response.getBody(), new SheinGetShippingBasicTypeReference());
                LOGGER.info(JsonMapper.toJson(sheinResponse));
                if (!"0".equals(sheinResponse.getCode())) {
                    throw new BusinessServiceException(sheinResponse.getMsg());
                }
                return sheinResponse.getInfo();
            } catch (IOException e) {
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 查询物流公司信息 shein
     *
     * @param request
     * @return
     */
    public SheinExpressCompanyListResponse expressCompanyList(SheinExpressCompanyListRequest request) {
        String uri = String.format("%s/shein/open-api/shipping/express-company-list", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                SheinResponse<SheinExpressCompanyListResponse> sheinResponse = objectMapper.readValue(response.getBody(), new SheinExpressCompanyListTypeReference());
                LOGGER.info(JsonMapper.toJson(sheinResponse));
                if (Objects.isNull(sheinResponse.getCode()))
                    return null;
                if (!"0".equals(sheinResponse.getCode())) {
                    throw new BusinessServiceException(sheinResponse.getMsg());
                }
                return sheinResponse.getInfo();
            } catch (IOException e) {
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 国内发货 shein
     *
     * @param request
     * @return
     */
    public SheinOrderToShippingResponse orderToShippingShein(SheinOrderToShippingRequest request) {
        String uri = String.format("%s/shein/open-api/shipping/orderToShipping", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SHEIN_CREATE_SHIPORDER, uri,
                JsonMapper.toJson(request), request.getList().get(0).getOrderNo(), "Temu创建发货单");
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                SheinResponse<SheinOrderToShippingResponse> sheinResponse = objectMapper.readValue(response.getBody(), new SheinOrderToShippingTypeReference());
                LOGGER.info(JsonMapper.toJson(sheinResponse));
                if (!"0".equals(sheinResponse.getCode())) {
                    externalApiLogService.updateLog(apiLogEntity, sheinResponse.getMsg(), ExternalApiLogStatusEnum.FAIL);
                    throw new BusinessServiceException(sheinResponse.getMsg());
                }
                externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
                return sheinResponse.getInfo();
            } catch (IOException e) {
                externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            externalApiLogService.updateLog(apiLogEntity, "请求失败", ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 订单打印箱唛或包裹 shein
     *
     * @param request
     * @return
     */
    public SheinPrintPackageResponse printPackageShein(SheinPrintPackageRequest request) {
        String uri = String.format("%s/shein/open-api/order/print-package", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                SheinResponse<SheinPrintPackageResponse> sheinResponse = objectMapper.readValue(response.getBody(), new SheinPrintPackageResponseTypeReference());
                LOGGER.info(JsonMapper.toJson(sheinResponse));
                if (!"0".equals(sheinResponse.getCode())) {
                    throw new BusinessServiceException(sheinResponse.getMsg());
                }
                return sheinResponse.getInfo();
            } catch (IOException e) {
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 国内发货 tiktok全托管
     *
     * @param request
     * @return
     */
    public TikTokShopCreateOrderResponse createTikTokDeliveryOrder(TikTokShopCreateOrderRequest request) {
        String uri = String.format("%s/tiktok-shop/create-order", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.TIKTOKSHOP_CREATE_SHIPORDER, uri,
                JsonMapper.toJson(request), request.getDeliveryOrder().getStockupOrderCode(), "TikTok全托管创建发货单");
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                TikTokShopCreateOrderResponse tikTokResponse = objectMapper.readValue(response.getBody(), TikTokShopCreateOrderResponse.class);
                externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
                return tikTokResponse;
            } catch (IOException e) {
                externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
                LOGGER.error("请求失败", e);
                String errMessage = e.getMessage();
                if (StringUtils.hasText(e.getMessage()) && e.getMessage().contains("SSLException")) {
                    errMessage = "第三方系统繁忙请重试!";
                }
                throw new BusinessServiceException(errMessage, e);
            }
        } else {
            externalApiLogService.updateLog(apiLogEntity, "请求失败", ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * tiktok打印送货单
     *
     * @param request
     * @return
     */
    public TikTokShopPrintResponse printTikTokDeliveryOrder(TikTokShopDeliveryOrderPrintRequest request) {
        String uri = String.format("%s/tiktok-shop/print-delivery-order", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                return objectMapper.readValue(response.getBody(), TikTokShopPrintResponse.class);
            } catch (IOException e) {
                throw new BusinessServiceException(String.format("请求失败: %s", e.getMessage()), e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * tiktok打印物流单
     *
     * @param request
     * @return
     */
    public TikTokShopPrintResponse printLogisticsLabel(TikTokShopGetLogisticsInfoRequest request) {
        String uri = String.format("%s/tiktok-shop/logistics-order-print", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                return objectMapper.readValue(response.getBody(), TikTokShopPrintResponse.class);
            } catch (IOException e) {
                throw new BusinessServiceException(String.format("请求失败: %s", e.getMessage()), e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 查询送货单信息
     *
     * @param request
     * @return
     */
    public TikTokShopStockUpOrderResponse stockUpOrderQuery(TikTokShopStockUpOrderQueryRequest request) {
        String uri = String.format("%s/tiktok-shop/query-stockup-order", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                return objectMapper.readValue(response.getBody(), TikTokShopStockUpOrderResponse.class);
            } catch (IOException e) {
                LOGGER.error("请求失败", e);
                String errMessage = e.getMessage();
                if (StringUtils.hasText(e.getMessage()) && e.getMessage().contains("SSLException")) {
                    errMessage = "第三方系统繁忙请重试!";
                }
                throw new BusinessServiceException(errMessage, e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 查询送货单信息
     *
     * @param request
     * @return
     */
    public TikTokShopDeliveryOrderResponse deliveryOrderQuery(TikTokShopDeliveryOrderQueryRequest request) {
        String uri = String.format("%s/tiktok-shop/delivery-order-query", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                return objectMapper.readValue(response.getBody(), TikTokShopDeliveryOrderResponse.class);
            } catch (IOException e) {
                LOGGER.error("请求失败", e);
                String errMessage = e.getMessage();
                if (StringUtils.hasText(e.getMessage()) && e.getMessage().contains("SSLException")) {
                    errMessage = "第三方系统繁忙请重试!";
                }
                throw new BusinessServiceException(errMessage, e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * tiktok全托管获取物流供应商
     *
     * @param request
     * @return
     */
    public TikTokShopShippingProvidersResponse getShippingProviders(TikTokShopShippingProvidersRequest request) {
        String uri = String.format("%s/tiktok-shop/get-shipping-providers", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                return objectMapper.readValue(response.getBody(), TikTokShopShippingProvidersResponse.class);
            } catch (IOException e) {
                LOGGER.error("请求失败", e);
                String errMessage = e.getMessage();
                if (StringUtils.hasText(e.getMessage()) && e.getMessage().contains("SSLException")) {
                    errMessage = "第三方系统繁忙请重试!";
                }
                throw new BusinessServiceException(errMessage, e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * tiktok全托管预约发货
     *
     * @param request
     * @return
     */
    public TikTokReserveShipResponse reserveShip(TikTokShopReserveShipRequest request) {
        String uri = String.format("%s/tiktok-shop/reserve-ship", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                return objectMapper.readValue(response.getBody(), TikTokReserveShipResponse.class);
            } catch (IOException e) {
                LOGGER.error("请求失败", e);
                String errMessage = e.getMessage();
                if (StringUtils.hasText(e.getMessage()) && e.getMessage().contains("SSLException")) {
                    errMessage = "第三方系统繁忙请重试!";
                }
                throw new BusinessServiceException(errMessage, e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * tiktok全托管预约发货
     *
     * @param request
     * @return
     */
    public TikTokLocalShippingPrintLabelResponse tiktokDirectPrintLabel(TikTokLocalShippingPrintLabelRequest request) {
        String uri = String.format("%s/tiktok/direct-ship-print-label", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                return objectMapper.readValue(response.getBody(), TikTokLocalShippingPrintLabelResponse.class);
            } catch (IOException e) {
                LOGGER.error("请求失败", e);
                String errMessage = e.getMessage();
                if (StringUtils.hasText(e.getMessage()) && e.getMessage().contains("SSLException")) {
                    errMessage = "第三方系统繁忙请重试!";
                }
                throw new BusinessServiceException(errMessage, e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * tiktok直邮发货
     *
     * @param request
     * @return
     */
    public TikTokLocalShippingShipResponse tiktokDirectShipOrder(TikTokLocalShippingShipRequest request) {
        String uri = String.format("%s/platform/delivery", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                return objectMapper.readValue(response.getBody(), TikTokLocalShippingShipResponse.class);
            } catch (IOException e) {
                LOGGER.error("请求失败", e);
                String errMessage = e.getMessage();
                if (StringUtils.hasText(e.getMessage()) && e.getMessage().contains("SSLException")) {
                    errMessage = "第三方系统繁忙请重试!";
                }
                throw new BusinessServiceException(errMessage, e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 获取采购单详情
     *
     * @param request
     * @return
     */
    public SheinPurchaseOrderInfosResponse getPurchaseOrderInfosShein(SheinPurchaseOrderInfosRequest request) {
        String uri = String.format("%s/shein/open-api/order/purchase-order-infos", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                SheinResponse<SheinPurchaseOrderInfosResponse> sheinResponse = objectMapper.readValue(response.getBody(), new SheinPurchaseOrderInfosReference());
                LOGGER.info(JsonMapper.toJson(sheinResponse));
                if (!"0".equals(sheinResponse.getCode())) {
                    throw new BusinessServiceException(sheinResponse.getMsg());
                }
                return sheinResponse.getInfo();
            } catch (IOException e) {
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }


    /**
     * otto 获取库存
     *
     * @param request
     * @return
     */
    public OttoGetStockResponse.OttoData getStockOtto(OttoGetStockRequest request) {
        String uri = String.format("%s/otto/get-stock", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                OttoGetStockResponse ottoResponse = objectMapper.readValue(response.getBody(), OttoGetStockResponse.class);
                LOGGER.info(JsonMapper.toJson(ottoResponse));
                if (0 != ottoResponse.getCode()) {
                    throw new BusinessServiceException(ottoResponse.getMessage());
                }
                return ottoResponse.getData();
            } catch (IOException e) {
                throw new BusinessServiceException(String.format("请求失败: %s", e.getMessage()), e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }


    /**
     * otto 创建订单
     *
     * @param request
     * @return
     */
    public OttoCreateOrderResponse.OttoData createOrderOtto(OttoCreateOrderRequest request) {
        String uri = String.format("%s/otto/create-order", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                OttoCreateOrderResponse ottoResponse = objectMapper.readValue(response.getBody(), OttoCreateOrderResponse.class);
                LOGGER.info(JsonMapper.toJson(ottoResponse));
                if (0 != ottoResponse.getCode()) {
                    throw new BusinessServiceException(ottoResponse.getMessage());
                }
                return ottoResponse.getData();
            } catch (IOException e) {
                throw new BusinessServiceException(String.format("请求失败: %s", e.getMessage()), e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }


    /**
     * otto 创建TMS订单
     *
     * @param request
     * @return
     */
    public OttoCreateTmsOrderResponse.OttoData createTmsOrderOtto(OttoCreateTmsOrderRequest request) {
        String uri = String.format("%s/otto/create-tms-order", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                OttoCreateTmsOrderResponse ottoResponse = objectMapper.readValue(response.getBody(), OttoCreateTmsOrderResponse.class);
                LOGGER.info(JsonMapper.toJson(ottoResponse));
                if (0 != ottoResponse.getCode()) {
                    throw new BusinessServiceException(ottoResponse.getMessage());
                }
                return ottoResponse.getData();
            } catch (IOException e) {
                throw new BusinessServiceException(String.format("请求失败: %s", e.getMessage()), e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }


    /**
     * otto 获取订单
     *
     * @param request
     * @return
     */
    public List<OttoGetOrderResponse.OttoData> getOrderOtto(OttoGetOrderRequest request) {
        String uri = String.format("%s/otto/get-order", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                OttoGetOrderResponse ottoResponse = objectMapper.readValue(response.getBody(), OttoGetOrderResponse.class);
                LOGGER.info(JsonMapper.toJson(ottoResponse));
                if (0 != ottoResponse.getCode()) {
                    throw new BusinessServiceException(ottoResponse.getMessage());
                }
                return ottoResponse.getData();
            } catch (IOException e) {
                throw new BusinessServiceException(String.format("请求失败: %s", e.getMessage()), e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 领星获取调整单
     *
     * @param request
     * @return
     */
    public PageResponse<LxAdjustmentOrderResponse> getAdjustmentOrderList(LxAdjustmentOrderRequest request) {
        String uri = String.format("%s/ling-xing-api/get-adjustment-order-list", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                return objectMapper.readValue(response.getBody(), new LxAdjustmentOrderReference());
            } catch (IOException e) {
                throw new BusinessServiceException(String.format("请求失败: %s", e.getMessage()), e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * tiktok退货单
     *
     * @param request
     * @return
     */
    public TikTokShopReturnOrderSearchResponse searchReturnOrders(TikTokShopReturnOrderSearchRequest request) {
        String uri = String.format("%s/tiktok-shop/search-return-orders", thirdPartyServiceUrl);
        LOGGER.info(JsonMapper.toJson(request));
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                return objectMapper.readValue(response.getBody(), TikTokShopReturnOrderSearchResponse.class);
            } catch (IOException e) {
                throw new BusinessServiceException(String.format("请求失败: %s", e.getMessage()), e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }


    private static final class TemuGoodsLabelGetResponseTypeReference extends TypeReference<CommonResponse<String>> {
    }

    private static final class TemuShiporderCreateResponseTypeReference extends TypeReference<CommonResponse<TemuShiporderCreateResponse>> {
    }

    private static final class TemuShiporderGetResponseTypeReference extends TypeReference<CommonResponse<TemuShiporderGetResponse>> {
    }

    private static final class TemuLogisticsCompanyGetResponseTypeReference extends TypeReference<CommonResponse<TemuLogisticsCompanyGetResponse>> {
    }

    private static final class TemuGetShiporderReceiveaddressTypeReference extends TypeReference<CommonResponse<TemuShiporderReceiveaddressGetResponse>> {
    }

    private static final class TemuGetBoxmarkinfoTypeReference extends TypeReference<CommonResponse<String>> {
    }

    private static final class TemuSendShiporderResponseReference extends TypeReference<CommonResponse<TemuSendShiporderResponse>> {
    }

    private static final class TemuMallAddressGetResponseReference extends TypeReference<CommonResponse<TemuMallAddressGetResponse>> {
    }

    private static final class TemuGetPurchaseOrderInfoListResponseReference extends TypeReference<CommonResponse<TemuGetPurchaseOrderInfoListResponse>> {
    }

    private static final class TemuGetLogisticsMatchResponseReference extends TypeReference<CommonResponse<List<TemuGetLogisticsMatchResponse.ResultItem>>> {
    }

    private static final class TemuGetReceiveAddressV2ResponseReference extends TypeReference<CommonResponse<TemuGetReceiveAddressV2Response.Result>> {
    }

    private static final class SheinPrintBarcodeResponseTypeReference extends TypeReference<SheinResponse<SheinPrintBarcodeResponse>> {
    }

    private static final class SheinGetShippingBasicTypeReference extends TypeReference<SheinResponse<SheinShippingBasicResponse>> {
    }

    private static final class SheinOrderToShippingTypeReference extends TypeReference<SheinResponse<SheinOrderToShippingResponse>> {
    }

    private static final class SheinExpressCompanyListTypeReference extends TypeReference<SheinResponse<SheinExpressCompanyListResponse>> {
    }

    private static final class SheinPrintPackageResponseTypeReference extends TypeReference<SheinResponse<SheinPrintPackageResponse>> {
    }

    private static final class SheinPurchaseOrderInfosReference extends TypeReference<SheinResponse<SheinPurchaseOrderInfosResponse>> {
    }

    private static final class LxAdjustmentOrderReference extends TypeReference<PageResponse<LxAdjustmentOrderResponse>> {
    }

    private static final class ReserveShipReference extends TypeReference<CommonResponse<String>> {
    }
}
