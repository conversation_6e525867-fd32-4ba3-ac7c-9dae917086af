package com.nsy.wms.business.manage.thirdparty.request.tiktokshp;

import com.nsy.wms.business.manage.thirdparty.request.TikTokBaseRequest;

import java.util.List;

/**
 * TikTok Shop 退货单列表查询请求
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
public class TikTokShopReturnOrderSearchRequest extends TikTokBaseRequest {

    /**
     * 页号，从1开始
     */
    private String pageToken;

    /**
     * 每页记录数, <= 50
     */
    private Integer pageSize;

    /**
     * 创建时间开始时间
     */
    private Integer orderCreateTimeGe;

    /**
     * 创建时间结束时间
     */
    private Integer orderCreateTimeLt;

    /**
     * 退货单号，支持批量查询（最大到50个）
     */
    private List<String> returnOrderCodes;

    /**
     * SPU编号，支持批量查询（最大到50个）
     */
    private List<String> platformSpuCodes;

    /**
     * SKU编号，支持批量查询（最大到50个）
     */
    private List<String> platformSkuCodes;

    /**
     * 商家SKC货号，支持批量查询（最大到50个）
     */
    private List<String> externalSkcCodes;

    /**
     * 商家SKU货号，支持批量查询（最大到50个）
     */
    private List<String> externalSkuCodes;

    /**
     * 退货单状态列表
     */
    private List<String> returnStatus;

    /**
     * 退货单类型列表
     */
    private List<String> returnTypes;

    /**
     * 退货来源
     */
    private String returnSource;

    /**
     * 退货方式列表
     */
    private List<String> returnMethods;

    public String getPageToken() {
        return pageToken;
    }

    public void setPageToken(String pageToken) {
        this.pageToken = pageToken;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getOrderCreateTimeGe() {
        return orderCreateTimeGe;
    }

    public void setOrderCreateTimeGe(Integer orderCreateTimeGe) {
        this.orderCreateTimeGe = orderCreateTimeGe;
    }

    public Integer getOrderCreateTimeLt() {
        return orderCreateTimeLt;
    }

    public void setOrderCreateTimeLt(Integer orderCreateTimeLt) {
        this.orderCreateTimeLt = orderCreateTimeLt;
    }

    public List<String> getReturnOrderCodes() {
        return returnOrderCodes;
    }

    public void setReturnOrderCodes(List<String> returnOrderCodes) {
        this.returnOrderCodes = returnOrderCodes;
    }

    public List<String> getPlatformSpuCodes() {
        return platformSpuCodes;
    }

    public void setPlatformSpuCodes(List<String> platformSpuCodes) {
        this.platformSpuCodes = platformSpuCodes;
    }

    public List<String> getPlatformSkuCodes() {
        return platformSkuCodes;
    }

    public void setPlatformSkuCodes(List<String> platformSkuCodes) {
        this.platformSkuCodes = platformSkuCodes;
    }

    public List<String> getExternalSkcCodes() {
        return externalSkcCodes;
    }

    public void setExternalSkcCodes(List<String> externalSkcCodes) {
        this.externalSkcCodes = externalSkcCodes;
    }

    public List<String> getExternalSkuCodes() {
        return externalSkuCodes;
    }

    public void setExternalSkuCodes(List<String> externalSkuCodes) {
        this.externalSkuCodes = externalSkuCodes;
    }

    public List<String> getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(List<String> returnStatus) {
        this.returnStatus = returnStatus;
    }

    public List<String> getReturnTypes() {
        return returnTypes;
    }

    public void setReturnTypes(List<String> returnTypes) {
        this.returnTypes = returnTypes;
    }

    public String getReturnSource() {
        return returnSource;
    }

    public void setReturnSource(String returnSource) {
        this.returnSource = returnSource;
    }

    public List<String> getReturnMethods() {
        return returnMethods;
    }

    public void setReturnMethods(List<String> returnMethods) {
        this.returnMethods = returnMethods;
    }
} 