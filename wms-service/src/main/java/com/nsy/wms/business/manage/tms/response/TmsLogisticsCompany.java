package com.nsy.wms.business.manage.tms.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 1.0
 */
public class TmsLogisticsCompany {

    @ApiModelProperty(value = "id", name = "id")
    private Integer id;

    @ApiModelProperty(value = "地区", name = "location")
    private String location;

    @ApiModelProperty(value = "物流公司", name = "logisticsCompany")
    private String logisticsCompany;

    @ApiModelProperty(value = "物流类型", name = "logisticsType")
    private String logisticsType;

    @ApiModelProperty(value = "物流方式", name = "logisticsMethod")
    private String logisticsMethod;

    @ApiModelProperty(value = "物流公司代码", name = "logisticsCode")
    private String logisticsCode;

    @ApiModelProperty(value = "物流公司联系地址", name = "logisticsAddress")
    private String logisticsAddress;

    @ApiModelProperty(value = "物流公司联系电话", name = "logisticsMobile")
    private String logisticsMobile;

    @ApiModelProperty(value = "工作区域", name = "workspace")
    private String workspace;

    @ApiModelProperty(value = "限重(g)", name = "limitWeight")
    private BigDecimal limitWeight;

    @ApiModelProperty(value = "打印规格 宽", name = "labelWidth")
    private Integer labelWidth;

    @ApiModelProperty(value = "打印规格 高", name = "labelHeight")
    private Integer labelHeight;

    @ApiModelProperty(value = "打印规格", name = "printSpec")
    private String printSpec;

    @ApiModelProperty(value = "优先级", name = "priority")
    private Integer priority;

    @ApiModelProperty(value = "是否默认 1-是，0-否", name = "isDefault")
    private Integer isDefault;

    @ApiModelProperty(value = "分销可用 1-是，0-否", name = "distributionAvailable")
    private Integer distributionAvailable;

    @ApiModelProperty(value = "物流查询显示 1-是，0-否", name = "queryDisplay")
    private Integer queryDisplay;

    @ApiModelProperty(value = "是否启用:1--启用 0--停用", name = "status")
    private Integer status;

    @ApiModelProperty(value = "描述", name = "description")
    private String description;

    @ApiModelProperty(value = "版本号", name = "version")
    private Integer version;

    @ApiModelProperty(value = "创建时间", name = "createDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty(value = "创建者", name = "createBy")
    private String createBy;

    @ApiModelProperty(value = "更新时间", name = "updateDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    @ApiModelProperty(value = "更新者", name = "updateBy")
    private String updateBy;

    @ApiModelProperty("是否费用核对")
    private Integer verifyPrice;

    @ApiModelProperty(value = "物流类型", name = "channelType")
    private String channelType;

    @ApiModelProperty("渠道id")
    private Integer channelId;

    @ApiModelProperty(value = "tms物流公司", name = "tmsLogisticsCompany")
    private String tmsLogisticsCompany;

    public String getTmsLogisticsCompany() {
        return tmsLogisticsCompany;
    }

    public void setTmsLogisticsCompany(String tmsLogisticsCompany) {
        this.tmsLogisticsCompany = tmsLogisticsCompany;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public Integer getVerifyPrice() {
        return verifyPrice;
    }

    public void setVerifyPrice(Integer verifyPrice) {
        this.verifyPrice = verifyPrice;
    }

    public String getLocation() {
        return this.location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
    public String getLogisticsCompany() {
        return this.logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }
    public String getLogisticsCode() {
        return this.logisticsCode;
    }

    public String getLogisticsType() {
        return logisticsType;
    }

    public void setLogisticsType(String logisticsType) {
        this.logisticsType = logisticsType;
    }

    public String getPrintSpec() {
        return printSpec;
    }

    public void setPrintSpec(String printSpec) {
        this.printSpec = printSpec;
    }

    public String getLogisticsMethod() {
        return logisticsMethod;
    }

    public void setLogisticsMethod(String logisticsMethod) {
        this.logisticsMethod = logisticsMethod;
    }

    public void setLogisticsCode(String logisticsCode) {
        this.logisticsCode = logisticsCode;
    }
    public String getLogisticsAddress() {
        return this.logisticsAddress;
    }

    public void setLogisticsAddress(String logisticsAddress) {
        this.logisticsAddress = logisticsAddress;
    }
    public String getLogisticsMobile() {
        return this.logisticsMobile;
    }

    public void setLogisticsMobile(String logisticsMobile) {
        this.logisticsMobile = logisticsMobile;
    }

    public String getWorkspace() {
        return workspace;
    }

    public void setWorkspace(String workspace) {
        this.workspace = workspace;
    }

    public BigDecimal getLimitWeight() {
        return this.limitWeight;
    }

    public void setLimitWeight(BigDecimal limitWeight) {
        this.limitWeight = limitWeight;
    }
    public Integer getLabelWidth() {
        return this.labelWidth;
    }

    public void setLabelWidth(Integer labelWidth) {
        this.labelWidth = labelWidth;
    }
    public Integer getLabelHeight() {
        return this.labelHeight;
    }

    public void setLabelHeight(Integer labelHeight) {
        this.labelHeight = labelHeight;
    }
    public Integer getPriority() {
        return this.priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    public Integer getDistributionAvailable() {
        return distributionAvailable;
    }

    public void setDistributionAvailable(Integer distributionAvailable) {
        this.distributionAvailable = distributionAvailable;
    }

    public Integer getQueryDisplay() {
        return queryDisplay;
    }

    public void setQueryDisplay(Integer queryDisplay) {
        this.queryDisplay = queryDisplay;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
    public Integer getVersion() {
        return this.version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public String getCreateBy() {
        return this.createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
    public String getUpdateBy() {
        return this.updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
}
