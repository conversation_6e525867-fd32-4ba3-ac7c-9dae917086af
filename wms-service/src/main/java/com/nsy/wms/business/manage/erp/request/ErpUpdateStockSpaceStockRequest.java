package com.nsy.wms.business.manage.erp.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ErpUpdateStockSpaceStockRequest {


    //是否加工调拨：0:否；1:是
    @JsonProperty("IsProcessTransfer")
    private Integer isProcessTransfer = 0;

    //是否无调出来源：0:否；1:是
    @JsonProperty("IsTransferOutByNoSource")
    private Integer isTransferOutByNoSource = 0;

    /**
     * 区域
     */
    @JsonProperty("Location")
    private String location;

    /**
     * 操作人
     */
    @JsonProperty("Operator")
    private String operator;

    /**
     * 调出编码
     */
    @JsonProperty("TransferOutPositionCode")
    private String transferOutPositionCode;

    /**
     * 调出仓库ID
     */
    @JsonProperty("TransferOutSpaceId")
    private Integer transferOutSpaceId;

    /**
     * 调入编码
     */
    @JsonProperty("TransferInPositionCode")
    private String transferInPositionCode;

    /**
     * 调入仓库ID
     */
    @JsonProperty("TransferInSpaceId")
    private Integer transferInSpaceId;

    /**
     * Sku
     */
    @JsonProperty("Sku")
    private String sku;

    /**
     * 调拨数量
     */
    @JsonProperty("Qty")
    private Integer qty;

    @JsonProperty("ErpTransferId")
    private Integer erpTransferId;

    public Integer getIsProcessTransfer() {
        return isProcessTransfer;
    }

    public void setIsProcessTransfer(Integer isProcessTransfer) {
        this.isProcessTransfer = isProcessTransfer;
    }

    public Integer getIsTransferOutByNoSource() {
        return isTransferOutByNoSource;
    }

    public void setIsTransferOutByNoSource(Integer isTransferOutByNoSource) {
        this.isTransferOutByNoSource = isTransferOutByNoSource;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getTransferOutPositionCode() {
        return transferOutPositionCode;
    }

    public void setTransferOutPositionCode(String transferOutPositionCode) {
        this.transferOutPositionCode = transferOutPositionCode;
    }

    public Integer getTransferOutSpaceId() {
        return transferOutSpaceId;
    }

    public void setTransferOutSpaceId(Integer transferOutSpaceId) {
        this.transferOutSpaceId = transferOutSpaceId;
    }

    public String getTransferInPositionCode() {
        return transferInPositionCode;
    }

    public void setTransferInPositionCode(String transferInPositionCode) {
        this.transferInPositionCode = transferInPositionCode;
    }

    public Integer getTransferInSpaceId() {
        return transferInSpaceId;
    }

    public void setTransferInSpaceId(Integer transferInSpaceId) {
        this.transferInSpaceId = transferInSpaceId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public Integer getErpTransferId() {
        return erpTransferId;
    }

    public void setErpTransferId(Integer erpTransferId) {
        this.erpTransferId = erpTransferId;
    }
}
