package com.nsy.wms.business.service.bd;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.request.bd.DeleteSkcFifoInfoRequest;
import com.nsy.api.wms.request.bd.QuerySkcFifoInfoRequest;
import com.nsy.api.wms.request.bd.SaveSkcFifoInfoRequest;
import com.nsy.api.wms.request.bd.SkcFifoInfoDto;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.wms.repository.entity.bd.BdSkcFifoRiskTypeEntity;
import com.nsy.wms.repository.entity.bd.BdStockFifoEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description skc先进先出配置服务
 * @since 2025/7/7
 */
@Service
public class BdSkcFifoService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BdSkcFifoService.class);

    @Autowired
    private BdStockFifoService bdStockFifoService;

    @Autowired
    private BdSkcFifoRiskTypeService bdSkcFifoRiskTypeService;

    @Transactional(rollbackFor = Exception.class)
    public void saveSkcFifoInfo(SaveSkcFifoInfoRequest request) {
        List<SkcFifoInfoDto> skcFifoInfoList = request.getSkcFifoInfoList();
        String operator = request.getOperator();
        String location = request.getLocation();
        if (skcFifoInfoList == null || skcFifoInfoList.isEmpty()) {
            throw new BusinessServiceException("先进先出信息不能为空");
        }
        // 1. 先删除原有SKC的配置（假设以skc为唯一标识）
        List<String> skcList = skcFifoInfoList.stream().map(SkcFifoInfoDto::getSkc).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        deleteOldSkcFifoInfo(skcList, location);
        // 2. 批量保存SKC FIFO配置和风险项
        List<BdStockFifoEntity> fifoEntities = Lists.newArrayListWithExpectedSize(skcFifoInfoList.size());
        List<BdSkcFifoRiskTypeEntity> riskTypeEntities = Lists.newArrayList();
        for (SkcFifoInfoDto dto : skcFifoInfoList) {
            if (CollectionUtils.isNotEmpty(dto.getRiskTypeList())) {
                for (String riskType : dto.getRiskTypeList()) {
                    BdSkcFifoRiskTypeEntity riskTypeEntity = new BdSkcFifoRiskTypeEntity();
                    riskTypeEntity.setSkc(dto.getSkc());
                    riskTypeEntity.setRiskType(riskType);
                    riskTypeEntity.setCreateBy(operator);
                    riskTypeEntity.setUpdateBy(operator);
                    riskTypeEntity.setLocation(location);
                    riskTypeEntities.add(riskTypeEntity);
                }
            }
            if (dto.getIsFifo() == null) {
                continue;
            }
            BdStockFifoEntity fifoEntity = new BdStockFifoEntity();
            fifoEntity.setSkc(dto.getSkc());
            fifoEntity.setIsEnable(Objects.equals(dto.getIsFifo(), 1));
            fifoEntity.setCreateBy(operator);
            fifoEntity.setUpdateBy(operator);
            fifoEntity.setSpaceId(0);
            fifoEntity.setSpaceName("");
            fifoEntity.setCategoryId(0);
            fifoEntity.setLocation(location);
            fifoEntities.add(fifoEntity);
        }
        if (!fifoEntities.isEmpty()) {
            bdStockFifoService.saveBatch(fifoEntities);
        }
        if (!riskTypeEntities.isEmpty()) {
            bdSkcFifoRiskTypeService.saveBatch(riskTypeEntities);
        }
    }

    private void deleteOldSkcFifoInfo(List<String> skcList, String location) {
        if (CollectionUtils.isEmpty(skcList)) {
            LOGGER.info("deleteOldSkcFifoInfo ignore, empty skcList");
            return;
        }

        LOGGER.info("deleteOldSkcFifoInfo skcList: {}, location: {}", JSONUtil.toJsonStr(skcList), location);

        // 先查询验证是否存在数据
        List<BdStockFifoEntity> existingFifoList = bdStockFifoService.list(new LambdaQueryWrapper<BdStockFifoEntity>()
                .in(BdStockFifoEntity::getSkc, skcList)
                .eq(BdStockFifoEntity::getLocation, location));

        List<BdSkcFifoRiskTypeEntity> existingRiskTypeList = bdSkcFifoRiskTypeService.list(new LambdaQueryWrapper<BdSkcFifoRiskTypeEntity>()
                .in(BdSkcFifoRiskTypeEntity::getSkc, skcList)
                .eq(BdSkcFifoRiskTypeEntity::getLocation, location));

        LOGGER.info("Found existing fifo records: {}, riskType records: {}", existingFifoList.size(), existingRiskTypeList.size());

        if (CollectionUtils.isNotEmpty(existingFifoList)) {
            LOGGER.info("Existing fifo records: {}", JSONUtil.toJsonStr(existingFifoList.stream().map(BdStockFifoEntity::getSkc).collect(Collectors.toList())));
            boolean deletedFifo = bdStockFifoService.removeByIds(existingFifoList.stream().map(BdStockFifoEntity::getId).collect(Collectors.toList()));
            LOGGER.info("deleteOldSkcFifoInfo completed, deleted fifo: {}", deletedFifo);
        } else {
            LOGGER.info("deleteOldSkcFifoInfo completed, no fifo records to delete");
        }

        if (CollectionUtils.isNotEmpty(existingRiskTypeList)) {
            LOGGER.info("Existing riskType records: {}", JSONUtil.toJsonStr(existingRiskTypeList.stream().map(BdSkcFifoRiskTypeEntity::getSkc).collect(Collectors.toList())));
            boolean deletedRiskType = bdSkcFifoRiskTypeService.removeByIds(existingRiskTypeList.stream().map(BdSkcFifoRiskTypeEntity::getId).collect(Collectors.toList()));
            LOGGER.info("deleteOldSkcFifoInfo completed, deleted riskType: {}", deletedRiskType);
        } else {
            LOGGER.info("deleteOldSkcFifoInfo completed, no riskType records to delete");
        }
    }

    public List<SkcFifoInfoDto> querySkcFifoInfoList(QuerySkcFifoInfoRequest request) {
        List<String> skcList = request.getSkcList();
        if (CollectionUtils.isEmpty(skcList)) {
            return Lists.newArrayList();
        }

        // 1. 查询SKC FIFO配置
        List<BdStockFifoEntity> fifoEntityList = bdStockFifoService.list(
                new LambdaQueryWrapper<BdStockFifoEntity>()
                        .in(BdStockFifoEntity::getSkc, skcList)
        );

        // 2. 查询SKC风险项配置
        List<BdSkcFifoRiskTypeEntity> riskTypeEntityList = bdSkcFifoRiskTypeService.list(
                new LambdaQueryWrapper<BdSkcFifoRiskTypeEntity>()
                        .in(BdSkcFifoRiskTypeEntity::getSkc, skcList)
        );
        // 3. 按SKC分组风险项
        Map<String, List<BdSkcFifoRiskTypeEntity>> riskTypeMap = riskTypeEntityList.stream()
                .collect(Collectors.groupingBy(BdSkcFifoRiskTypeEntity::getSkc));

        // 4. 组装返回结果
        List<SkcFifoInfoDto> resultList = Lists.newArrayListWithExpectedSize(request.getSkcList().size());
        request.getSkcList().forEach(skc -> {
            SkcFifoInfoDto dto = new SkcFifoInfoDto();
            dto.setSkc(skc);
            BdStockFifoEntity bdStockFifoEntity = CollectionUtils.isEmpty(fifoEntityList) ? null : fifoEntityList.stream().filter(item -> item.getSkc().equals(skc)).findFirst().orElse(null);
            if (bdStockFifoEntity != null) {
                dto.setIsFifo(Boolean.TRUE.equals(bdStockFifoEntity.getIsEnable()) ? 1 : 0);
            }
            // 设置风险项列表
            List<BdSkcFifoRiskTypeEntity> riskTypes = riskTypeMap.get(skc);
            if (CollectionUtils.isNotEmpty(riskTypes)) {
                List<String> riskTypeList = riskTypes.stream()
                        .map(BdSkcFifoRiskTypeEntity::getRiskType).distinct()
                        .collect(Collectors.toList());
                dto.setRiskTypeList(riskTypeList);
            }
            resultList.add(dto);
        });
        return resultList;
    }

    public PageResponse<SkcFifoInfoDto> pageQuerySkcFifoInfoList(QuerySkcFifoInfoRequest request) {
        return bdSkcFifoRiskTypeService.pageQuerySkcFifoInfoList(request);
    }

    public void deleteSkcFifoInfo(DeleteSkcFifoInfoRequest request) {
        List<BdStockFifoEntity> bdStockFifoEntities = bdStockFifoService.listBySkcList(request.getSkcList());
        if (CollectionUtils.isNotEmpty(bdStockFifoEntities)) {
            bdStockFifoService.removeByIds(bdStockFifoEntities.stream().map(BdStockFifoEntity::getId).collect(Collectors.toList()));
        }
        List<BdSkcFifoRiskTypeEntity> bdSkcFifoRiskTypeEntities = bdSkcFifoRiskTypeService.listBySkcList(request.getSkcList());
        if (CollectionUtils.isNotEmpty(bdSkcFifoRiskTypeEntities)) {
            bdSkcFifoRiskTypeService.removeByIds(bdSkcFifoRiskTypeEntities.stream().map(BdSkcFifoRiskTypeEntity::getId).collect(Collectors.toList()));
        }
    }
}
