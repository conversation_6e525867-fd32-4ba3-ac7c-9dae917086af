package com.nsy.wms.business.service.stockout;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.MD5Util;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.MybatisQueryConstant;
import com.nsy.api.wms.domain.stockout.StockoutDeclareOrderRecommendPrint;
import com.nsy.api.wms.domain.stockout.StockoutFbaOrderInfoPrint;
import com.nsy.api.wms.enumeration.PrintTemplateNameEnum;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiInfoEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiLogStatusEnum;
import com.nsy.api.wms.enumeration.stockout.FbaLabelStatusEnum;
import com.nsy.api.wms.enumeration.stockout.FbaReplenishTypeEnum;
import com.nsy.api.wms.enumeration.stockout.ReplenishOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderPlatformEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutShipmentStatusEnum;
import com.nsy.api.wms.request.stockout.FbaShipmentOrderRequest;
import com.nsy.api.wms.request.stockout.StaBoxInfoDetailRequest;
import com.nsy.api.wms.request.stockout.StaBoxInfoRequest;
import com.nsy.api.wms.request.stockout.StockoutFbaLabelStatusRequest;
import com.nsy.api.wms.request.stockout.StringListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stockout.StaShipmentRelationResponse;
import com.nsy.wms.business.manage.amazon.AmazonApiService;
import com.nsy.wms.business.manage.amazon.dto.StaBoxCompleteMessage;
import com.nsy.wms.business.manage.amazon.dto.StaBoxInfoDto;
import com.nsy.wms.business.manage.amazon.dto.StaBoxItemInfoDto;
import com.nsy.wms.business.manage.amazon.dto.StaBoxResult;
import com.nsy.wms.business.manage.amazon.dto.StaShipmentBoxRelationDto;
import com.nsy.wms.business.manage.amazon.dto.StaShipmentBoxRelationMessage;
import com.nsy.wms.business.manage.amazon.request.AmazonFulfillmentCenterRequest;
import com.nsy.wms.business.manage.amazon.response.AmazonFulfillmentCenterVO;
import com.nsy.wms.business.manage.amazon.response.GetStaDeliveryRelateShipmentResponse;
import com.nsy.wms.business.manage.amazon.response.StaBoxInfoDetailResponse;
import com.nsy.wms.business.manage.amazon.response.StaBoxInfoResponse;
import com.nsy.wms.business.manage.oms.request.OmsShipmentBoxRelationDto;
import com.nsy.wms.business.manage.oms.request.OmsShipmentBoxRelationMessage;
import com.nsy.wms.business.manage.thirdparty.OmsApiService;
import com.nsy.wms.business.manage.tms.TmsCacheService;
import com.nsy.wms.business.manage.tms.response.TmsLogisticsCompany;
import com.nsy.wms.business.service.bd.BdCountryService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.config.PrintTemplateService;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdCountryEntity;
import com.nsy.wms.repository.entity.config.PrintTemplateEntity;
import com.nsy.wms.repository.entity.external.ExternalApiLogEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderPackMappingInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentAmazonRelationEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentErpPickingBoxEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentAmazonRelationMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.Key;
import com.nsy.wms.utils.PrintTransferUtils;
import com.nsy.wms.utils.encryption.AesEncryptUtil;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 装箱清单同步亚马逊
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class StockoutShipmentAmazonRelationService extends ServiceImpl<StockoutShipmentAmazonRelationMapper, StockoutShipmentAmazonRelationEntity> {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutShipmentAmazonRelationService.class);

    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    MessageProducer messageProducer;
    @Autowired
    TmsCacheService tmsCacheService;
    @Autowired
    StockoutShipmentErpPickingBoxService erpPickingBoxService;
    @Autowired
    StockoutShipmentItemService shipmentItemService;
    @Autowired
    StockoutShipmentService shipmentService;
    @Autowired
    StockoutOrderService orderService;
    @Autowired
    StockoutOrderLogService orderLogService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockoutShipmentOperateService shipmentOperateService;
    @Autowired
    StockoutOrderItemService orderItemService;
    @Autowired
    StockoutShipmentAmazonRelationService stockoutShipmentAmazonRelationService;
    @Autowired
    StockoutShipmentCustomsService stockoutShipmentCustomsService;
    @Autowired
    BdCountryService countryService;
    @Autowired
    StockoutOrderPackMappingInfoService packMappingInfoService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutCustomsDeclareOrderService customsDeclareOrderService;
    @Autowired
    AmazonApiService amazonApiService;
    @Resource
    PrintTemplateService printTemplateService;
    @Resource
    ExternalApiLogService externalApiLogService;
    @Autowired
    private StockoutOrderBoxPositionService shipmentBoxPositionService;
    @Autowired
    private StockoutShipmentInfoService shipmentInfoService;
    @Autowired
    private OmsApiService omsApiService;


    public void buildBoxInfoDto(StaBoxInfoDto dto, StockoutShipmentEntity shipmentEntity, Integer pickingBoxId) {
        dto.setPickingBoxId(pickingBoxId);
        BeanUtilsEx.copyProperties(shipmentEntity, dto);
        dto.setBoxCode(shipmentEntity.getShipmentBoxCode());
        if (StringUtils.hasText(shipmentEntity.getBoxSize()) && shipmentEntity.getBoxSize().contains("*")) {
            String[] split = shipmentEntity.getBoxSize().split("[*]");
            dto.setBoxLength(split.length > 0 ? new BigDecimal(split[0]) : null);
            dto.setBoxWidth(split.length > 1 ? new BigDecimal(split[1]) : null);
            dto.setBoxHeight(split.length > 2 ? new BigDecimal(split[2]) : null);
        }
        dto.setDeliveryTime(shipmentEntity.getDeliveryDate());
        dto.setOperateDeliveryDate(shipmentEntity.getOperateDeliveryDate());
        dto.setStatus(shipmentEntity.getStatus());
        dto.setForwarderChannel(shipmentEntity.getForwarderChannel());
        dto.setBoxWeight(shipmentEntity.getWeight());
        if (StringUtils.hasText(shipmentEntity.getLogisticsCompany())) {
            List<TmsLogisticsCompany> allLogisticsCompanyList = tmsCacheService.getAllLogisticsCompanyList();
            allLogisticsCompanyList.stream().filter(item -> item.getLogisticsCompany().equalsIgnoreCase(shipmentEntity.getLogisticsCompany())).findFirst().ifPresent(item -> {
                if ("SEA_FREIGHT_FORWARDER".equalsIgnoreCase(item.getChannelType()) || "海运货代".equalsIgnoreCase(item.getChannelType())) {
                    dto.setTransportType(0);
                } else if ("AIR_FREIGHT_FORWARDER".equalsIgnoreCase(item.getChannelType()) || "空运货代".equalsIgnoreCase(item.getChannelType())) {
                    dto.setTransportType(1);
                } else if ("LAND_FREIGHT_FORWARDER".equalsIgnoreCase(item.getChannelType()) || "陆运货代".equalsIgnoreCase(item.getChannelType())) {
                    dto.setTransportType(2);
                } else if ("RAIL_FREIGHT_FORWARDER".equalsIgnoreCase(item.getChannelType()) || "铁运货代".equalsIgnoreCase(item.getChannelType())) {
                    dto.setTransportType(3);
                } else {
                    dto.setTransportType(4);
                }
            });
        }
        if (shipmentEntity.getIsDeleted() != 1) {
            List<StockoutShipmentItemEntity> shipmentItemEntityList = shipmentItemService.findByShipmentId(shipmentEntity.getShipmentId());
            List<String> stockoutOrderNos = shipmentItemEntityList.stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).distinct().collect(Collectors.toList());
            List<StockoutOrderEntity> packStockoutOrders = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNos).stream().filter(StockoutOrderEntity::getHasPack).collect(Collectors.toList());
            if (!packStockoutOrders.isEmpty()) {
                dto.setStaBoxItemInfoDtoList(this.buildPackInfoList(packStockoutOrders.stream().map(StockoutOrderEntity::getStockoutOrderId).collect(Collectors.toList()), shipmentItemEntityList.stream().filter(it -> it.getIsDeleted() != 1).collect(Collectors.toList())));
            } else {
                dto.setStaBoxItemInfoDtoList(shipmentItemEntityList.stream().filter(it -> it.getIsDeleted() != 1).map(itemEntity -> {
                    StockoutOrderItemEntity orderItemEntity = orderItemService.getById(itemEntity.getStockoutOrderItemId());
                    StaBoxItemInfoDto itemInfoDto = new StaBoxItemInfoDto();
                    itemInfoDto.setErpSku(itemEntity.getSku());
                    itemInfoDto.setQuantity(itemEntity.getQty());
                    itemInfoDto.setSellerSku(orderItemEntity.getSellerSku());
                    itemInfoDto.setOrderItemId(orderItemEntity.getOrderItemId());
                    return itemInfoDto;
                }).sorted(Comparator.comparing(StaBoxItemInfoDto::getErpSku)).collect(Collectors.toList()));
            }
        }
        // 最后加密字符串md
        dto.setMd5String(MD5Util.crypt(AesEncryptUtil.encrypt(JsonMapper.toJson(dto))));
    }

    /**
     * 转换为PACK信息
     */
    private List<StaBoxItemInfoDto> buildPackInfoList(List<Integer> stockoutOrderIds, List<StockoutShipmentItemEntity> shipmentItemEntityList) {
        List<String> allOrderItemIds = shipmentItemEntityList.stream().map(StockoutShipmentItemEntity::getOrderItemId).distinct().collect(Collectors.toList());
        List<StockoutOrderPackMappingInfoEntity> packMappingInfoEntityList = packMappingInfoService.list(new QueryWrapper<StockoutOrderPackMappingInfoEntity>().lambda()
            .in(StockoutOrderPackMappingInfoEntity::getOrderItemNo, allOrderItemIds)
            .in(StockoutOrderPackMappingInfoEntity::getStockoutOrderId, stockoutOrderIds));
        Map<String, List<StockoutOrderPackMappingInfoEntity>> map = packMappingInfoEntityList.stream()
            .sorted(Comparator.comparing(StockoutOrderPackMappingInfoEntity::getOriginSku))
            .collect(Collectors.groupingBy(StockoutOrderPackMappingInfoEntity::getOriginSku));
        List<StaBoxItemInfoDto> result = new LinkedList<>();
        // PACK商品，按pack sku分组，再计算装箱数
        for (Map.Entry<String, List<StockoutOrderPackMappingInfoEntity>> entry : map.entrySet()) {
            List<String> curOrderItemIds = entry.getValue().stream().map(StockoutOrderPackMappingInfoEntity::getOrderItemNo).distinct().collect(Collectors.toList());
            List<StockoutShipmentItemEntity> curShipmentItemList = shipmentItemEntityList.stream().filter(o -> curOrderItemIds.contains(o.getOrderItemId())).collect(Collectors.toList());
            StaBoxItemInfoDto itemInfoDto = new StaBoxItemInfoDto();
            itemInfoDto.setErpSku(entry.getKey());
            itemInfoDto.setQuantity(getPackShipQty(entry.getValue(), curShipmentItemList));
            itemInfoDto.setSellerSku(entry.getValue().get(0).getPackSellerSku());
            result.add(itemInfoDto);
        }
        // 非PACK商品
        List<String> packOrderItemIds = packMappingInfoEntityList.stream().map(StockoutOrderPackMappingInfoEntity::getOrderItemNo).distinct().collect(Collectors.toList());
        for (StockoutShipmentItemEntity itemEntity : shipmentItemEntityList) {
            if (packOrderItemIds.contains(itemEntity.getOrderItemId())) {
                continue;
            }
            StaBoxItemInfoDto itemInfoDto = new StaBoxItemInfoDto();
            StockoutOrderItemEntity orderItemEntity = orderItemService.getById(itemEntity.getStockoutOrderItemId());
            itemInfoDto.setErpSku(itemEntity.getSku());
            itemInfoDto.setQuantity(itemEntity.getQty());
            itemInfoDto.setSellerSku(orderItemEntity.getSellerSku());
            itemInfoDto.setOrderItemId(orderItemEntity.getOrderItemId());
            result.add(itemInfoDto);
        }
        return result;
    }

    /**
     * 根据映射关系，计算pack 发货数
     */
    private Integer getPackShipQty(List<StockoutOrderPackMappingInfoEntity> mappingInfoEntityList, List<StockoutShipmentItemEntity> curShipmentItemList) {
        List<Integer> shipQtyList = new LinkedList<>();
        curShipmentItemList.forEach(o -> {
            mappingInfoEntityList.stream().filter(m -> m.getOrderItemNo().equals(o.getOrderItemId())).findFirst().ifPresent(curMapping -> shipQtyList.add(o.getQty() / curMapping.getMappingQty()));
        });
        return shipQtyList.stream().min(Integer::compareTo).orElse(0);
    }

    public StaBoxInfoResponse syncByErpTid(StaBoxInfoRequest request) {
        StaBoxInfoResponse message = new StaBoxInfoResponse();
        StockoutOrderEntity orderEntity = orderService.getBaseMapper().findTopByOrderNoWithoutLocation(request.getErpTid());
        if (orderEntity == null) {
            return message;
        }
        TenantContext.setTenant(orderEntity.getLocation());
        LambdaQueryWrapper<StockoutShipmentErpPickingBoxEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutShipmentErpPickingBoxEntity::getErpTid, request.getErpTid());
        List<StockoutShipmentErpPickingBoxEntity> list = erpPickingBoxService.list(wrapper);
        list.forEach(pickBox -> {
            if (!CollectionUtils.isEmpty(request.getPickingBoxIdList()) && !request.getPickingBoxIdList().contains(pickBox.getErpPickingBoxId())) {
                return;
            }
            StockoutShipmentEntity shipmentEntity = shipmentService.getById(pickBox.getShipmentId());
            StaBoxResult boxResult = erpPickingBoxService.getBaseMapper().findStaBoxResult(shipmentEntity.getShipmentId());
            message.setStockoutOrderStatus(boxResult.getStockoutOrderStatus());
            message.setErpTid(pickBox.getErpTid());
        });
        return message;
    }


    public void syncAmazonRelation(StaShipmentBoxRelationMessage messageContent) {
        String erpTid = messageContent.getErpTid();
        TenantContext.setTenant(messageContent.getLocation());
        LambdaQueryWrapper<StockoutShipmentAmazonRelationEntity> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(erpTid))
            wrapper.eq(StockoutShipmentAmazonRelationEntity::getErpTid, erpTid);
        else
            wrapper.in(StockoutShipmentAmazonRelationEntity::getShipmentId,
                messageContent.getStaShipmentBoxRelationDtoList().stream().map(StaShipmentBoxRelationDto::getShipmentId).collect(Collectors.toList()));
        remove(wrapper);
        List<Integer> shipmentIdList = new ArrayList<>();
        messageContent.getStaShipmentBoxRelationDtoList().forEach(dto -> dto.getPickingBoxIdList().stream().distinct().forEach(boxId -> {
            StockoutShipmentAmazonRelationEntity entity = new StockoutShipmentAmazonRelationEntity();
            List<StockoutShipmentErpPickingBoxEntity> existPickingBoxList = erpPickingBoxService.list(new QueryWrapper<StockoutShipmentErpPickingBoxEntity>().lambda()
                .eq(StockoutShipmentErpPickingBoxEntity::getErpPickingBoxId, boxId).orderByDesc(StockoutShipmentErpPickingBoxEntity::getErpPickingBoxId));
            if (CollectionUtils.isEmpty(existPickingBoxList)) {
                LOGGER.error("找不到erp对应的wms装箱： {} ", boxId);
                return;
                //throw new BusinessServiceException("找不到erp对应的wms装箱：" + boxId);
            }
            if (StrUtil.isAllBlank(dto.getShipToAddressLine1(), dto.getShipToStateOrProvinceCode()) && StrUtil.isNotBlank(dto.getDestinationFulfillmentCenterId())) {
                AmazonFulfillmentCenterVO centerVO = findAmazonCenterIdAddress(dto.getDestinationFulfillmentCenterId());
                if (centerVO != null) {
                    dto.setShipToAddressLine1(centerVO.getAddressLine1());
                    dto.setShipToCity(centerVO.getCity());
                    dto.setShipToCountryCode(centerVO.getCountryCode());
                    dto.setShipToPostalCode(centerVO.getPostalCode());
                    dto.setShipToStateOrProvinceCode(centerVO.getStateOrProvinceCode());
                }
            }
            entity.setErpTid(StringUtils.hasText(erpTid) ? erpTid : existPickingBoxList.get(0).getErpTid());
            entity.setFbaShipmentId(dto.getShipmentId());
            entity.setShipmentId(existPickingBoxList.get(0).getShipmentId());
            shipmentIdList.add(entity.getShipmentId());
            entity.setShipToAddressLine1(dto.getShipToAddressLine1());
            entity.setShipToCity(dto.getShipToCity());
            entity.setShipToName(dto.getShipToName());
            entity.setShipToCountryCode(dto.getShipToCountryCode());
            entity.setShipToPostalCode(dto.getShipToPostalCode());
            entity.setShipToStateOrProvinceCode(dto.getShipToStateOrProvinceCode());
            entity.setErpPickingBoxId(boxId);
            entity.setAmazonReferenceId(dto.getAmazonRefrenceId());
            entity.setDestinationFulfillmentCenterId(dto.getDestinationFulfillmentCenterId());
            entity.setLocation(TenantContext.getTenant());
            entity.setCreateBy(dto.getCreateBy());
            entity.setUpdateBy(dto.getUpdateBy());
            save(entity);
            stockoutShipmentCustomsService.generateCustomsOrder(entity);
        }));
        if (messageContent.getPlanType() != null && messageContent.getPlanType() == 1) {
            
        }
        TenantContext.clear();
    }

    public AmazonFulfillmentCenterVO findAmazonCenterIdAddress(String destinationFulfillmentCenterId) {
        try {
            if (StrUtil.isBlank(destinationFulfillmentCenterId)) {
                return null;
            }
            AmazonFulfillmentCenterRequest amazonFulfillmentCenterRequest = new AmazonFulfillmentCenterRequest();
            amazonFulfillmentCenterRequest.setDestinationFulfillmentCenterIds(Collections.singletonList(destinationFulfillmentCenterId));
            PageResponse fulfillmentCenter = amazonApiService.getFulfillmentCenter(amazonFulfillmentCenterRequest);
            List<AmazonFulfillmentCenterVO> centerVOS = JsonMapper.jsonStringToObjectArray(JsonMapper.toJson(fulfillmentCenter.getContent()), AmazonFulfillmentCenterVO.class);
            if (CollectionUtils.isEmpty(centerVOS)) {
                return null;
            }
            return centerVOS.get(0);
        } catch (Exception e) {
            LOGGER.error("获取亚马逊仓库地址错误！", e);
            return null;
        }
    }

    public Map<String, Long> getFbaShipmentId(String scanNo) {
        if (!StringUtils.hasText(scanNo)) {
            throw new BusinessServiceException("请输入订单号或者出库单号");
        }
        Map<String, Long> resultMap = new HashMap<>();
        LambdaQueryWrapper<StockoutShipmentItemEntity> queryWrapper = new LambdaQueryWrapper<StockoutShipmentItemEntity>()
            .eq(StockoutShipmentItemEntity::getStockoutOrderNo, scanNo).or().eq(StockoutShipmentItemEntity::getOrderNo, scanNo);
        List<Integer> shipmentIds = shipmentItemService.list(queryWrapper).stream().filter(it -> it.getIsDeleted() == 0).map(StockoutShipmentItemEntity::getShipmentId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shipmentIds)) {
            throw new BusinessServiceException("请输入订单号或者出库单号");
        }
        LambdaQueryWrapper<StockoutShipmentAmazonRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockoutShipmentAmazonRelationEntity::getShipmentId, shipmentIds);
        List<StockoutShipmentAmazonRelationEntity> list = stockoutShipmentAmazonRelationService.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            // 没有映射关系，旧label直接打印
            long count = shipmentItemService.findByOrderNo(scanNo).stream().map(StockoutShipmentItemEntity::getShipmentId).distinct().count();
            resultMap.put(scanNo, count);
            return resultMap;
        }
        StockoutShipmentAmazonRelationEntity stockoutShipmentAmazonRelationEntity = list.stream().filter(it -> StrUtil.isNotBlank(it.getFbaShipmentId())).findFirst()
            .orElseThrow(() -> new BusinessServiceException("还未生成Shipment！"));
        List<GetStaDeliveryRelateShipmentResponse> allShipment = amazonApiService.getPlanAllShipment(stockoutShipmentAmazonRelationEntity.getFbaShipmentId());
        if (CollectionUtils.isEmpty(allShipment)) {
            return resultMap;
        }
        allShipment.forEach(it -> resultMap.put(it.getShipmentId(), Long.valueOf(it.getBoxSize())));
        return resultMap;
    }

    public Map<String, Long> getBatchFbaShipmentId(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            throw new BusinessServiceException("请输入正确订单号");
        }
        List<StockoutCustomsDeclareOrderEntity> entityList = customsDeclareOrderService.getOrderNoByIdList(idList);
        if (CollectionUtils.isEmpty(entityList)) {
            throw new BusinessServiceException("获取不到相关的报关订单信息");
        }
        List<String> orderNoList = entityList.stream().map(StockoutCustomsDeclareOrderEntity::getOrderNo).distinct().collect(Collectors.toList());
        Map<String, Long> resultMap = new HashMap<>(32);
        LambdaQueryWrapper<StockoutShipmentItemEntity> queryWrapper = new LambdaQueryWrapper<StockoutShipmentItemEntity>()
            .in(StockoutShipmentItemEntity::getOrderNo, orderNoList)
            .eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        List<StockoutShipmentItemEntity> shipmentEntityList = shipmentItemService.list(queryWrapper);
        Map<String, List<StockoutShipmentItemEntity>> stringListMap = shipmentEntityList.stream().collect(Collectors.groupingBy(StockoutShipmentItemEntity::getOrderNo));
        for (String orderNo : orderNoList) {
            if (!stringListMap.containsKey(orderNo)) {
                throw new BusinessServiceException("查询不到装箱信息!");
            }
            Set<Integer> shipmentIds = stringListMap.get(orderNo).stream().map(StockoutShipmentItemEntity::getShipmentId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(shipmentIds)) {
                throw new BusinessServiceException("查询不到装箱信息!");
            }
            LambdaQueryWrapper<StockoutShipmentAmazonRelationEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(StockoutShipmentAmazonRelationEntity::getShipmentId, shipmentIds);
            List<StockoutShipmentAmazonRelationEntity> list = stockoutShipmentAmazonRelationService.list(wrapper);
            if (CollectionUtils.isEmpty(list)) {
                // 没有映射关系，旧label直接打印
                long count = shipmentItemService.findByOrderNo(orderNo).stream().map(StockoutShipmentItemEntity::getShipmentId).distinct().count();
                resultMap.put(orderNo, count);
                return resultMap;
            }
            resultMap.putAll(list.stream().collect(Collectors.groupingBy(StockoutShipmentAmazonRelationEntity::getFbaShipmentId, Collectors.counting())));
        }
        return resultMap;
    }

    public String getReferenceIdByTid(List<String> collect) {
        LambdaQueryWrapper<StockoutShipmentAmazonRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockoutShipmentAmazonRelationEntity::getErpTid, collect);
        return list(wrapper).stream().map(StockoutShipmentAmazonRelationEntity::getAmazonReferenceId).distinct()
            .filter(StringUtils::hasText).collect(Collectors.joining(","));
    }

    public List<StaShipmentRelationResponse> getFbaShipment(String orderNo) {
        if (!StringUtils.hasText(orderNo))
            throw new BusinessServiceException("请输入订单号");
        LambdaQueryWrapper<StockoutShipmentAmazonRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutShipmentAmazonRelationEntity::getErpTid, orderNo)
            .or().eq(StockoutShipmentAmazonRelationEntity::getFbaShipmentId, orderNo);
        List<StockoutShipmentAmazonRelationEntity> list = stockoutShipmentAmazonRelationService.list(wrapper);
        if (CollectionUtils.isEmpty(list))
            return Collections.emptyList();
        List<StaShipmentRelationResponse> respList = new ArrayList<>(list.stream().map(entity -> {
            StaShipmentRelationResponse response = new StaShipmentRelationResponse();
            BeanUtilsEx.copyProperties(entity, response, "shipmentId");
            response.setShipmentId(entity.getFbaShipmentId());
            return response;
        }).collect(Collectors.toMap(StaShipmentRelationResponse::getShipmentId, Function.identity(), (entity1, entity2) -> entity1)).values());
        respList.forEach(resp -> {
            if (!StringUtils.hasText(resp.getShipToCountryCode()) && StringUtils.hasText(resp.getDestinationFulfillmentCenterId())) {
                AmazonFulfillmentCenterVO centerVO = findAmazonCenterIdAddress(resp.getDestinationFulfillmentCenterId());
                if (centerVO != null) {
                    BeanUtilsEx.copyProperties(centerVO, resp);
                    resp.setShipToAddressLine1(centerVO.getAddressLine1());
                    resp.setShipToCity(centerVO.getCity());
                    resp.setShipToCountryCode(centerVO.getCountryCode());
                    resp.setShipToPostalCode(centerVO.getPostalCode());
                    resp.setShipToStateOrProvinceCode(centerVO.getStateOrProvinceCode());
                }
            }
            if (StringUtils.hasText(resp.getShipToCountryCode())) {
                LambdaQueryWrapper<BdCountryEntity> wp = new LambdaQueryWrapper<>();
                wp.eq(BdCountryEntity::getCountryCode, resp.getShipToCountryCode());
                List<BdCountryEntity> list1 = countryService.list(wp);
                if (!CollectionUtils.isEmpty(list1))
                    resp.setShipToCountry(list1.get(0).getCountryEnglish());
            }
        });
        return respList;
    }

    public List<StockoutShipmentAmazonRelationEntity> listByFbaShipmentId(List<String> collect) {
        if (CollectionUtils.isEmpty(collect)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<StockoutShipmentAmazonRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockoutShipmentAmazonRelationEntity::getFbaShipmentId, collect);
        return stockoutShipmentAmazonRelationService.list(wrapper);
    }

    public List<StockoutShipmentAmazonRelationEntity> listByShipmentId(List<Integer> shipmentIds) {
        if (CollectionUtils.isEmpty(shipmentIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<StockoutShipmentAmazonRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockoutShipmentAmazonRelationEntity::getShipmentId, shipmentIds);
        return stockoutShipmentAmazonRelationService.list(wrapper);
    }

    public PrintListResponse recommendPrintByShipmentId(StringListRequest request) {
        PrintListResponse response = new PrintListResponse();
        List<StockoutShipmentAmazonRelationEntity> amazonRelationEntities = listByFbaShipmentId(request.getStringList());
        if (CollectionUtils.isEmpty(amazonRelationEntities)) {
            throw new BusinessServiceException("查询不到需要打印的Fba Shipment信息!");
        }
        List<String> fbaShipmentIds = amazonRelationEntities.stream().map(StockoutShipmentAmazonRelationEntity::getFbaShipmentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (fbaShipmentIds.size() > 1) {
            throw new BusinessServiceException("只支持单个Fba shipment打印分货标！");
        }
        PrintTemplateEntity templateEntity = printTemplateService.getByName(PrintTemplateNameEnum.AMAZON_SHIPMENT_DISTRIBUTE.getTemplateName());
        response.setHtmlList(this.buildRecommendHtml(amazonRelationEntities, templateEntity));
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    private List<String> buildRecommendHtml(List<StockoutShipmentAmazonRelationEntity> amazonRelationEntities, PrintTemplateEntity templateEntity) {
        List<String> htmlList = new ArrayList<>();
        List<Integer> shipmentIds = amazonRelationEntities.stream().map(StockoutShipmentAmazonRelationEntity::getShipmentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<StockoutShipmentEntity> shipmentEntities = shipmentService.list(new LambdaQueryWrapper<StockoutShipmentEntity>()
            .in(StockoutShipmentEntity::getShipmentId, shipmentIds).orderByAsc(StockoutShipmentEntity::getBoxIndex));
        AtomicInteger boxIndex = new AtomicInteger(1);
        shipmentEntities.forEach(shipment -> {
            StockoutDeclareOrderRecommendPrint print = new StockoutDeclareOrderRecommendPrint();
            print.setOrderNo(amazonRelationEntities.get(0).getFbaShipmentId());
            print.setAmazonSpaceName(amazonRelationEntities.get(0).getDestinationFulfillmentCenterId());
            print.setCurrentBoxNum(boxIndex.getAndIncrement());
            print.setBoxNum(shipment.getBoxIndex());
            print.setTotalBoxCount(shipmentEntities.size());
            print.setStoreType("");
            print.setLogisticsType(shipment.getLogisticsCompany());
            htmlList.add(PrintTransferUtils.transfer(templateEntity.getContent(), print));
        });
        return htmlList;
    }

    public PrintListResponse fbaOrderInfo(FbaShipmentOrderRequest request) {
        PrintListResponse response = new PrintListResponse();
        PrintTemplateEntity templateEntity = printTemplateService.getByName(PrintTemplateNameEnum.AMAZON_ORDER_INFO.getTemplateName());
        List<StockoutShipmentAmazonRelationEntity> amazonRelationEntities = listByFbaShipmentId(request.getFbaShipmentId());
        if (CollectionUtils.isEmpty(amazonRelationEntities)) {
            throw new BusinessServiceException("查询不到需要打印的Fba Shipment信息!");
        }
        List<String> fbaShipmentIds = amazonRelationEntities.stream().map(StockoutShipmentAmazonRelationEntity::getFbaShipmentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (fbaShipmentIds.size() > 1) {
            throw new BusinessServiceException("只支持单个Fba shipment打印订单信息！");
        }
        List<Integer> shipmentIds = amazonRelationEntities.stream().map(StockoutShipmentAmazonRelationEntity::getShipmentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<StockoutShipmentEntity> shipmentList = shipmentService.findByShipmentIdsList(shipmentIds);
        if (CollectionUtils.isEmpty(shipmentList)) {
            throw new BusinessServiceException("无法找单装箱清单！");
        }
        StockoutFbaOrderInfoPrint print = new StockoutFbaOrderInfoPrint();
        print.setFbaShipmentId(amazonRelationEntities.get(0).getFbaShipmentId());
        print.setLogisticsCompany(shipmentList.get(0).getLogisticsCompany());
        print.setSkuTotalQty(shipmentService.getBaseMapper().countQtyByIds(shipmentIds));
        print.setBoxNum(shipmentIds.size());
        print.setSkuTotalWeight(shipmentList.stream().map(StockoutShipmentEntity::getWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        print.setDestinationFulfillmentCenterId(amazonRelationEntities.get(0).getDestinationFulfillmentCenterId());
        print.setOrderNo(amazonRelationEntities.get(0).getErpTid());
        String orderNo = FbaReplenishTypeEnum.SPLIT_SHIPMENT.getName().equalsIgnoreCase(shipmentList.get(0).getFbaReplenishType()) ? amazonRelationEntities.get(0).getFbaShipmentId() : amazonRelationEntities.get(0).getErpTid();
        print.setPositionCode(shipmentBoxPositionService.getPositionCodeByOrderNo(orderNo));
        String result = PrintTransferUtils.transfer(templateEntity.getContent(), print);
        List<String> list = CollUtil.newArrayList(new String[request.getRepeatCount()]);
        Collections.fill(list, result);
        response.setHtmlList(list);
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    /**
     * 亚马逊回传更新箱贴状态  (成功/失败、以及失败原因)
     *
     * <AUTHOR>
     * 2024-08-01
     */
    @Transactional
    @JLock(keyConstant = "updateFbaLabelStatus", lockKey = "#request.orderNo")
    public void updateFbaLabelStatus(StockoutFbaLabelStatusRequest request) {
        Map<String, String> fbaLabelStatusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_FBA_LABEL_STATUS.getName());
        if (!fbaLabelStatusMap.containsKey(request.getLabelStatus())) {
            throw new BusinessServiceException("labelStatus申请状态值不符合要求！");
        }
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.UPDATE_FBA_LABEL_STATUS, "/stockout-shipment/fba-label-status",
            JsonMapper.toJson(request), request.getOrderNo(), "更新Fba箱贴申请状态");
        try {
            // amazon的fba更新箱贴状态，先判断是否新流程
            boolean fbaNewFlow = false;
            List<Integer> shipmentIdList = shipmentService.list(new LambdaQueryWrapper<StockoutShipmentEntity>()
                .select(StockoutShipmentEntity::getShipmentId)
                .eq(StockoutShipmentEntity::getIsDeleted, 0)
                .eq(StockoutShipmentEntity::getReplenishOrder, request.getOrderNo())).stream().map(StockoutShipmentEntity::getShipmentId).collect(Collectors.toList());
            // 如果存在shipment,这一单就是新的FBA流程
            if (!CollectionUtils.isEmpty(shipmentIdList)) {
                fbaNewFlow = true;
            } else {
                shipmentIdList = shipmentItemService.getBaseMapper().searchShipmentIdCodeListByOrderNo(Collections.singletonList(request.getOrderNo()));
            }
            if (CollectionUtils.isEmpty(shipmentIdList)) {
                throw new BusinessServiceException("暂无要更新箱贴的箱子，");
            }
            // 新旧流程都更新箱子状态
            shipmentService.update(new LambdaUpdateWrapper<StockoutShipmentEntity>()
                .set(StockoutShipmentEntity::getFbaLabelStatus, request.getLabelStatus())
                .set(StrUtil.equals(request.getLabelStatus(), FbaLabelStatusEnum.EXCEPTION.name()), StockoutShipmentEntity::getRemark, StrUtil.maxLength(request.getExceptionMsg(), 1020))
                .in(StockoutShipmentEntity::getShipmentId, shipmentIdList));
            // 找出全部的出库单，记录日志，旧流程更新出库单申请箱贴状态
            List<String> orderNos = shipmentItemService.getOrderNosByShipmentIds(shipmentIdList);
            List<StockoutOrderItemEntity> orderItemEntityList = orderItemService.getListByOrderNos(orderNos);
            List<StockoutOrderEntity> orderEntityList = orderService.listByIds(orderItemEntityList.stream()
                .map(StockoutOrderItemEntity::getStockoutOrderId).distinct().collect(Collectors.toList()));
            for (StockoutOrderEntity order : orderEntityList) {
                //是否FBA新流程
                if (fbaNewFlow && FbaReplenishTypeEnum.REPLENISH.getName().equalsIgnoreCase(order.getFbaReplenishType())
                    && FbaLabelStatusEnum.APPLYING.name().equals(request.getLabelStatus())) {
                    throw new BusinessServiceException("FBA补货单新流程会自动申请箱贴，请勿点击。");
                }
                String msg = StrUtil.equals(request.getLabelStatus(), FbaLabelStatusEnum.EXCEPTION.name()) ? "原因：" + request.getExceptionMsg() : "";
                orderLogService.addLog(order.getStockoutOrderNo(), StockoutOrderLogTypeEnum.FBA_LABEL_GET,
                    "补货单:" + request.getOrderNo() + "在amazon更新箱贴状态:" + FbaLabelStatusEnum.getDescByName(request.getLabelStatus()) + msg);
                if (!fbaNewFlow) {
                    order.setFbaLabelStatus(request.getLabelStatus());
                    orderService.updateById(order);
                }
            }
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (RuntimeException e) {
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(e.getMessage()), ExternalApiLogStatusEnum.FAIL);
            throw e;
        }
    }

    /**
     * 1 仓库端点击申请箱贴
     * 2 发送消息给amazon
     *
     * <AUTHOR>
     * 2025-01-24
     */
    @Transactional
    @JLock(keyConstant = "manualUpdateFbaLabelStatus", lockKey = "#request.orderNo")
    public void manualUpdateFbaLabelStatus(StockoutFbaLabelStatusRequest request) {
        List<StockoutOrderItemEntity> orderItemEntityList = orderItemService.getListByOrderNo(request.getOrderNo());
        if (CollectionUtils.isEmpty(orderItemEntityList)) {
            throw new BusinessServiceException(request.getOrderNo() + "无法找到对应的出库单，请核对");
        }
        List<Integer> orderIds = orderItemEntityList.stream().map(StockoutOrderItemEntity::getStockoutOrderId).distinct().collect(Collectors.toList());
        List<StockoutOrderEntity> orderEntityList = orderService.listByIds(orderIds);
        Map<String, String> fbaLabelStatusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_FBA_LABEL_STATUS.getName());
        if (!fbaLabelStatusMap.containsKey(request.getLabelStatus()))
            throw new BusinessServiceException("labelStatus申请状态值不符合要求！");
        List<StockoutShipmentItemEntity> shipmentItemList = shipmentItemService.findByOrderNo(request.getOrderNo());
        if (CollectionUtils.isEmpty(shipmentItemList))
            throw new BusinessServiceException("暂无要更新箱贴的箱子");
        List<Integer> shipmentIds = shipmentItemList.stream().map(StockoutShipmentItemEntity::getShipmentId).distinct().collect(Collectors.toList());
        List<StockoutShipmentEntity> shipmentList = shipmentService.findByShipmentIdsListOrderByIndex(shipmentIds);
        if (shipmentList.stream().anyMatch(item -> FbaLabelStatusEnum.CANCELED.name().equals(item.getStatus())))
            throw new BusinessServiceException("出库单已取消，无法再次申请箱贴");
        checkShipment(shipmentList, shipmentIds);
        shipmentService.update(new LambdaUpdateWrapper<StockoutShipmentEntity>()
            .set(StockoutShipmentEntity::getFbaLabelStatus, request.getLabelStatus())
            .in(StockoutShipmentEntity::getShipmentId, shipmentIds));

        StockoutOrderEntity stockoutOrderEntity = orderEntityList.get(0);
        if (StrUtil.equalsIgnoreCase(stockoutOrderEntity.getPlatformName(), StockoutOrderPlatformEnum.TIKTOK.getName())) {
            // tiktok 申请箱贴
            tiktokLabelApply(request, orderEntityList, orderItemEntityList, shipmentList);
        } else {
            // 亚马逊申请箱贴
            amazonLabelApply(request, orderEntityList, stockoutOrderEntity, orderItemEntityList);
        }
    }

    private void amazonLabelApply(StockoutFbaLabelStatusRequest request, List<StockoutOrderEntity> orderEntityList, StockoutOrderEntity stockoutOrderEntity, List<StockoutOrderItemEntity> orderItemEntityList) {
        //发货计划手动生成补货单
        if (FbaReplenishTypeEnum.REPLENISH.getName().equalsIgnoreCase(stockoutOrderEntity.getFbaReplenishType())
            && FbaLabelStatusEnum.APPLYING.name().equals(request.getLabelStatus())) {
            throw new BusinessServiceException("FBA补货单新流程会自动申请箱贴，请勿点击。");
        }

        //先分仓后装箱 - 需判断相同订单号下的出库单都为待发货
        if (FbaReplenishTypeEnum.SPLIT_SHIPMENT.getName().equalsIgnoreCase(stockoutOrderEntity.getFbaReplenishType())
            && FbaLabelStatusEnum.APPLYING.name().equals(request.getLabelStatus())) {
            splitShipment(stockoutOrderEntity.getReplenishOrder(), request);

            return;
        }
        if (stockoutOrderEntity.getForceDockingType() != null && stockoutOrderEntity.getForceDockingType() == 2) {
            shipmentOperateService.checkShipmentMultipleOfFive(orderItemEntityList.get(0).getOrderNo());
        }
        validateOrderStatusAndUpdate(request, orderEntityList);
        if (request.getSyncAmazon()) {
            List<String> boxCodes = shipmentItemService.getBaseMapper().searchShipmentBoxCodeListByOrderNo(Collections.singletonList(request.getOrderNo()));
            // 发送消息
            StaBoxCompleteMessage message = new StaBoxCompleteMessage();
            message.setLocation(TenantContext.getTenant());
            message.setOrderNo(request.getOrderNo());
            message.setStaBoxCodeList(boxCodes);
            messageProducer.sendMessage(KafkaConstant.SYNC_AMAZON_BOX_COMPLETE_NAME, KafkaConstant.SYNC_AMAZON_BOX_COMPLETE, Key.of(message.getOrderNo()), message);
        }
    }


    private void tiktokLabelApply(StockoutFbaLabelStatusRequest request, List<StockoutOrderEntity> orderEntityList, List<StockoutOrderItemEntity> orderItemEntityList, List<StockoutShipmentEntity> shipmentList) {
        validateOrderStatusAndUpdate(request, orderEntityList);
        List<String> orderNoList = orderItemEntityList.stream().map(StockoutOrderItemEntity::getOrderNo).distinct().collect(Collectors.toList());
        if (orderNoList.size() > 1) {
            throw new BusinessServiceException("一次只能勾选一个订单进行申请箱贴");
        }
        StaBoxCompleteMessage message = new StaBoxCompleteMessage();
        message.setLocation(TenantContext.getTenant());
        message.setOrderNo(orderNoList.get(0));
        message.setStaBoxCodeList(shipmentList.stream().map(StockoutShipmentEntity::getShipmentBoxCode).collect(Collectors.toList()));
        messageProducer.sendMessage(KafkaConstant.SYNC_OMS_BOX_COMPLETE_NAME, KafkaConstant.SYNC_OMS_BOX_COMPLETE, Key.of(message.getOrderNo()), message);
    }

    private void validateOrderStatusAndUpdate(StockoutFbaLabelStatusRequest request, List<StockoutOrderEntity> orderEntityList) {
        for (StockoutOrderEntity order : orderEntityList) {
            if (StrUtil.equalsIgnoreCase(order.getFbaLabelStatus(), FbaLabelStatusEnum.COMPLETE.name()) && StrUtil.equalsAnyIgnoreCase(request.getLabelStatus(), FbaLabelStatusEnum.INIT.name(), FbaLabelStatusEnum.APPLYING.name())) {
                throw new BusinessServiceException("申请状态无法从已完成变为待申请/申请中！");
            }
            if (StrUtil.equalsAnyIgnoreCase(order.getStatus(), StockoutOrderStatusEnum.CANCELLED.name(), StockoutOrderStatusEnum.CANCELLING.name())) {
                continue;
            }
            if (!StrUtil.equalsAnyIgnoreCase(order.getStatus(), StockoutOrderStatusEnum.READY_DELIVERY.name(), StockoutOrderStatusEnum.DELIVERED.name())) {
                throw new BusinessServiceException("出库单状态不是不是待发货，无法申请面单");
            }
            order.setFbaLabelStatus(request.getLabelStatus());
            orderLogService.addLog(order.getStockoutOrderNo(), StockoutOrderLogTypeEnum.FBA_LABEL_GET, "仓库人员申请箱贴");
            orderService.updateById(order);
        }
    }

    private void checkShipment(List<StockoutShipmentEntity> shipmentList, List<Integer> shipmentIds) {
        List<StockoutShipmentErpPickingBoxEntity> erpBoxList = erpPickingBoxService.getTopByShipmentId(shipmentIds);
        if (erpBoxList.size() < shipmentList.size()) {
            throw new BusinessServiceException("正在同步箱子，请过1~2分钟后再申请箱贴");
        }
        AtomicInteger boxIndex = new AtomicInteger(1);
        shipmentList.forEach(shipment -> {
            if (StrUtil.equalsIgnoreCase(shipment.getFbaLabelStatus(), FbaLabelStatusEnum.WAIT.name())) {
                //如果是先分仓后装箱，再去判断一次箱贴状态
                if (StringUtils.hasText(shipment.getFbaReplenishType())
                    && FbaReplenishTypeEnum.SPLIT_SHIPMENT.getName().equals(shipment.getFbaReplenishType())
                    && stockoutOrderService.getBaseMapper().isReplenishOrderWaitDelivery(shipment.getReplenishOrder())) {
                    LOGGER.info("fba补货单 {} ，复核完成", shipment.getReplenishOrder());
                } else
                    throw new BusinessServiceException("当前补货单暂未全部复核完成，请等复核完再操作");
            }
            if (!StrUtil.equalsIgnoreCase(shipment.getFbaLabelStatus(), FbaLabelStatusEnum.INIT.name()))
                throw new BusinessServiceException("已经触发申请，请等待系统处理");
            if (shipment.getBoxIndex() == null)
                throw new BusinessServiceException(shipment.getShipmentBoxCode() + "该箱子没有填写序号！");
            int index = boxIndex.getAndIncrement();
            if (index != shipment.getBoxIndex())
                throw new BusinessServiceException(shipment.getShipmentBoxCode() + "该箱子序号不连续，请确认！");
        });
    }

    private void splitShipment(String replenishOrder, StockoutFbaLabelStatusRequest request) {
        //断相同订单号下的出库单是否都为待发货
        List<StockoutOrderEntity> list = stockoutOrderService.list(new LambdaQueryWrapper<StockoutOrderEntity>()
            .eq(StockoutOrderEntity::getReplenishOrder, replenishOrder)
            .notIn(StockoutOrderEntity::getStatus, Lists.newArrayList(StockoutOrderStatusEnum.CANCELLING.name(), StockoutOrderStatusEnum.CANCELLED.name())));

        if (list.stream().map(StockoutOrderEntity::getStatus).anyMatch(status -> !StockoutOrderStatusEnum.READY_DELIVERY.name().equalsIgnoreCase(status))) {
            Map<String, String> stockoutDocStatusEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_DOC_STATUS.getName());
            throw new BusinessServiceException(String.format("同补货单下的出库单未全部完成复核装箱，请检查:%s",
                list.stream().map(orderEntity -> String.format("%s(%s)", orderEntity.getStockoutOrderNo(),
                    stockoutDocStatusEnumMap.get(orderEntity.getStatus()))).collect(Collectors.joining(","))));
        }
        validateOrderStatusAndUpdate(request, list);
        if (request.getSyncAmazon()) {
            List<String> stockoutOrderNoList = list.stream().map(StockoutOrderEntity::getStockoutOrderNo).collect(Collectors.toList());
            List<String> boxCodes = shipmentItemService.getBaseMapper().searchShipmentBoxCodeListByStockoutOrderNo(stockoutOrderNoList);
            shipmentService.update(new LambdaUpdateWrapper<StockoutShipmentEntity>()
                .set(StockoutShipmentEntity::getFbaLabelStatus, request.getLabelStatus())
                .in(StockoutShipmentEntity::getShipmentBoxCode, boxCodes));
            StaBoxCompleteMessage message = new StaBoxCompleteMessage();
            message.setLocation(TenantContext.getTenant());
            message.setOrderNo(replenishOrder);
            message.setStaBoxCodeList(boxCodes);
            messageProducer.sendMessage(KafkaConstant.SYNC_AMAZON_BOX_COMPLETE_NAME, KafkaConstant.SYNC_AMAZON_BOX_COMPLETE, Key.of(message.getOrderNo()), message);
        }

    }

    @Transactional
    public void backFbaLabelStatus(Integer shipmentId, String orderNo) {
        StockoutShipmentEntity shipmentEntity = shipmentService.getByShipmentId(shipmentId);
        if (StockoutShipmentStatusEnum.SHIPPED.name().equals(shipmentEntity.getStatus())) {
            throw new BusinessServiceException("箱子已发货，不能回退箱贴！");
        }
        if (FbaLabelStatusEnum.INIT.name().equals(shipmentEntity.getFbaLabelStatus())) {
            throw new BusinessServiceException("箱子待申请箱贴，不能回退箱贴！");
        }
        if (StrUtil.equals(shipmentEntity.getPlatformName(), StockoutOrderPlatformEnum.TIKTOK.getName())) {
            // FBT回退箱贴
            shipmentInfoService.clearShipmentLabelMapping(orderNo, FbaLabelStatusEnum.INIT.name());
            // 调用oms同步
            omsApiService.backFbtLabel(orderNo);
            return;
        }
        // FBA回退箱贴
        FbaReplenishTypeEnum fbaReplenishType = StringUtils.hasText(shipmentEntity.getFbaReplenishType()) ? FbaReplenishTypeEnum.getByName(shipmentEntity.getFbaReplenishType()) : FbaReplenishTypeEnum.NORMAL;
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.BACK_FBA_LABEL_STATUS, "/stockout-shipment/fba-label-status",
            orderNo, shipmentEntity.getShipmentBoxCode(), "回退Fba箱贴");
        try {
            switch (fbaReplenishType) {
                case NORMAL:
                    backFbaLabelStatusNormal(orderNo);
                    break;
                case REPLENISH:
                    backFbaLabelStatusReplenis(shipmentEntity);
                    break;
                case SPLIT_SHIPMENT:
                    backFbaLabelStatusSplitShipment(shipmentEntity);
                    break;
                default:
                    break;
            }
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(e.getMessage()), ExternalApiLogStatusEnum.FAIL);
            throw e;
        }

    }

    //FBA补货类型：先分仓后装箱  回退申请箱贴
    private void backFbaLabelStatusSplitShipment(StockoutShipmentEntity shipmentEntity) {
        String replenishOrder = shipmentEntity.getReplenishOrder();
        if (!StringUtils.hasText(replenishOrder))
            throw new BusinessServiceException("找不到补货单号！");
        List<StockoutShipmentEntity> shipmentEntityList = shipmentService.list(new LambdaQueryWrapper<StockoutShipmentEntity>()
            .eq(StockoutShipmentEntity::getReplenishOrder, replenishOrder)
            .eq(StockoutShipmentEntity::getIsDeleted, 0));
        if (shipmentEntityList.stream().anyMatch(shipment -> StockoutShipmentStatusEnum.SHIPPED.name().equals(shipment.getStatus())))
            throw new BusinessServiceException(String.format("补货单%s下有箱子已发货，不允许回退！", replenishOrder));

        shipmentService.update(new LambdaUpdateWrapper<StockoutShipmentEntity>()
            .set(StockoutShipmentEntity::getFbaLabelStatus, FbaLabelStatusEnum.APPLYING.name())
            .set(StockoutShipmentEntity::getUpdateBy, loginInfoService.getName())
            .eq(StockoutShipmentEntity::getReplenishOrder, replenishOrder));

        List<Integer> shipmentIdList = shipmentEntityList.stream().map(StockoutShipmentEntity::getShipmentId).collect(Collectors.toList());

        LambdaQueryWrapper<StockoutShipmentAmazonRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockoutShipmentAmazonRelationEntity::getShipmentId, shipmentIdList);
        remove(wrapper);
        // 找出全部的出库单，记录日志，旧流程更新出库单申请箱贴状态
        List<StockoutOrderEntity> orderEntityList = orderService.list(new LambdaQueryWrapper<StockoutOrderEntity>()
            .eq(StockoutOrderEntity::getReplenishOrder, replenishOrder));
        for (StockoutOrderEntity order : orderEntityList) {
            if (StrUtil.equalsAny(order.getStatus(), StockoutOrderStatusEnum.CANCELLING.name(), StockoutOrderStatusEnum.CANCELLED.name())) {
                continue;
            }
            orderLogService.addLog(order.getStockoutOrderNo(), StockoutOrderLogTypeEnum.FBA_LABEL_GET,
                "补货单:" + replenishOrder + "回退箱贴状态:" + FbaLabelStatusEnum.INIT.getDesc());
            order.setFbaLabelStatus(FbaLabelStatusEnum.INIT.name());
            orderService.updateById(order);
        }
        List<String> boxCodes = shipmentEntityList.stream().map(StockoutShipmentEntity::getShipmentBoxCode).collect(Collectors.toList());
        amazonApiService.backFbaLabel(replenishOrder, boxCodes);
    }

    //FBA补货类型：补货计划  回退申请箱贴
    private void backFbaLabelStatusReplenis(StockoutShipmentEntity shipmentEntity) {
        String replenishOrder = shipmentEntity.getReplenishOrder();
        if (!StringUtils.hasText(replenishOrder))
            throw new BusinessServiceException("找不到补货单号！");
        List<StockoutShipmentEntity> shipmentEntityList = shipmentService.list(new LambdaQueryWrapper<StockoutShipmentEntity>()
            .eq(StockoutShipmentEntity::getReplenishOrder, replenishOrder)
            .eq(StockoutShipmentEntity::getIsDeleted, 0));
        if (shipmentEntityList.stream().anyMatch(shipment -> StockoutShipmentStatusEnum.SHIPPED.name().equals(shipment.getStatus())))
            throw new BusinessServiceException(String.format("补货单%s下有箱子已发货，不允许回退！", replenishOrder));

        shipmentService.update(new LambdaUpdateWrapper<StockoutShipmentEntity>()
            .set(StockoutShipmentEntity::getFbaLabelStatus, FbaLabelStatusEnum.INIT.name())
            .set(StockoutShipmentEntity::getReplenishOrderStatus, ReplenishOrderStatusEnum.WAIT_DEAL.name())
            .set(StockoutShipmentEntity::getUpdateBy, loginInfoService.getName())
            .set(StockoutShipmentEntity::getReplenishOrder, "")
            .eq(StockoutShipmentEntity::getReplenishOrder, replenishOrder));

        List<Integer> shipmentIdList = shipmentEntityList.stream().map(StockoutShipmentEntity::getShipmentId).collect(Collectors.toList());

        LambdaQueryWrapper<StockoutShipmentAmazonRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockoutShipmentAmazonRelationEntity::getShipmentId, shipmentIdList);
        remove(wrapper);
        // 找出全部的出库单，记录日志，旧流程更新出库单申请箱贴状态
        List<StockoutOrderEntity> orderEntityList = orderService.getBaseMapper().listByShipmentIdList(shipmentIdList);
        for (StockoutOrderEntity order : orderEntityList) {
            if (StrUtil.equalsAny(order.getStatus(), StockoutOrderStatusEnum.CANCELLING.name(), StockoutOrderStatusEnum.CANCELLED.name())) {
                continue;
            }
            orderLogService.addLog(order.getStockoutOrderNo(), StockoutOrderLogTypeEnum.FBA_LABEL_GET,
                "补货单:" + replenishOrder + "回退箱贴状态:" + FbaLabelStatusEnum.INIT.getDesc());
            order.setFbaLabelStatus(FbaLabelStatusEnum.INIT.name());
            orderService.updateById(order);
        }
        List<String> boxCodes = shipmentEntityList.stream().map(StockoutShipmentEntity::getShipmentBoxCode).collect(Collectors.toList());
        amazonApiService.backFbaLabel(replenishOrder, boxCodes);
    }

    //FBA补货类型：直接创建  回退申请箱贴
    private void backFbaLabelStatusNormal(String orderNo) {
        List<Integer> shipmentIdList = shipmentItemService.getBaseMapper().searchShipmentIdCodeListByOrderNo(Collections.singletonList(orderNo));
        if (CollectionUtils.isEmpty(shipmentIdList)) {
            throw new BusinessServiceException("暂无要更新的装箱清单！");
        }
        // 新旧流程都更新箱子状态
        shipmentService.update(new LambdaUpdateWrapper<StockoutShipmentEntity>()
            .set(StockoutShipmentEntity::getFbaLabelStatus, FbaLabelStatusEnum.INIT.name())
            .set(StockoutShipmentEntity::getUpdateBy, loginInfoService.getName())
            .in(StockoutShipmentEntity::getShipmentId, shipmentIdList));
        LambdaQueryWrapper<StockoutShipmentAmazonRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockoutShipmentAmazonRelationEntity::getShipmentId, shipmentIdList);
        remove(wrapper);
        // 找出全部的出库单，记录日志，旧流程更新出库单申请箱贴状态
        List<String> orderNos = shipmentItemService.getOrderNosByShipmentIds(shipmentIdList);
        List<StockoutOrderItemEntity> orderItemEntityList = orderItemService.getListByOrderNos(orderNos);
        List<StockoutOrderEntity> orderEntityList = orderService.listByIds(orderItemEntityList.stream()
            .map(StockoutOrderItemEntity::getStockoutOrderId).distinct().collect(Collectors.toList()));
        for (StockoutOrderEntity order : orderEntityList) {
            if (StrUtil.equalsAny(order.getStatus(), StockoutOrderStatusEnum.CANCELLING.name(), StockoutOrderStatusEnum.CANCELLED.name())) {
                continue;
            }
            orderLogService.addLog(order.getStockoutOrderNo(), StockoutOrderLogTypeEnum.FBA_LABEL_GET,
                "补货单:" + orderNo + "回退箱贴状态:" + FbaLabelStatusEnum.INIT.getDesc());
            order.setFbaLabelStatus(FbaLabelStatusEnum.INIT.name());
            orderService.updateById(order);
        }
        List<String> boxCodes = shipmentService.listByIds(shipmentIdList).stream().map(StockoutShipmentEntity::getShipmentBoxCode).collect(Collectors.toList());
        amazonApiService.backFbaLabel(orderNo, boxCodes);
    }

    public StockoutShipmentAmazonRelationEntity getOneByShipmentId(Integer shipmentId) {
        LambdaQueryWrapper<StockoutShipmentAmazonRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutShipmentAmazonRelationEntity::getShipmentId, shipmentId);
        wrapper.last(MybatisQueryConstant.QUERY_FIRST);
        return getOne(wrapper);
    }

    public StaBoxInfoDetailResponse getStaBoxInfoDetail(StaBoxInfoDetailRequest request) {
        StaBoxInfoDetailResponse response = new StaBoxInfoDetailResponse();
        request.getStaBoxCodeList().forEach(boxCode -> {
            StaBoxInfoDto dto = new StaBoxInfoDto();
            StockoutShipmentEntity shipmentEntity = shipmentService.findTopByShipmentBoxCode(boxCode);
            StockoutShipmentErpPickingBoxEntity topByShipmentId = erpPickingBoxService.getTopByShipmentId(shipmentEntity.getShipmentId());
            buildBoxInfoDto(dto, shipmentEntity, topByShipmentId.getErpPickingBoxId());
            if (!CollectionUtils.isEmpty(dto.getStaBoxItemInfoDtoList())) {
                if (StrUtil.isNotBlank(shipmentEntity.getReplenishOrder())) {
                    dto.setFbaShipmentId(topByShipmentId.getErpTid());
                }
                response.getStaBoxInfoDtoList().add(dto);
            }
        });
        return response;
    }

    public StaBoxInfoDetailResponse getStaBoxInfoDetailByOrderNo(StaBoxInfoDetailRequest request) {
        if (!CollectionUtils.isEmpty(request.getStaBoxCodeList())) {
            return getStaBoxInfoDetail(request);
        }
        if (StrUtil.isBlank(request.getOrderNo())) {
            throw new BusinessServiceException("订单号不能为空");
        }
        List<String> codeList = shipmentItemService.getBaseMapper().searchShipmentBoxCodeListByOrderNo(Collections.singletonList(request.getOrderNo()));
        if (!CollectionUtils.isEmpty(codeList)) {
            request.setStaBoxCodeList(codeList);
            return getStaBoxInfoDetail(request);
        }
        LambdaQueryWrapper<StockoutShipmentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutShipmentEntity::getReplenishOrder, request.getOrderNo());
        wrapper.eq(StockoutShipmentEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        List<StockoutShipmentEntity> list = shipmentService.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessServiceException(request.getOrderNo() + "找不到装箱数据!");
        }
        request.setStaBoxCodeList(list.stream().map(StockoutShipmentEntity::getShipmentBoxCode).collect(Collectors.toList()));
        return getStaBoxInfoDetail(request);
    }

    public void syncOmsRelation(OmsShipmentBoxRelationMessage messageContent) {
        String erpTid = messageContent.getErpTid();
        LambdaQueryWrapper<StockoutShipmentAmazonRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutShipmentAmazonRelationEntity::getErpTid, erpTid);
        wrapper.in(StockoutShipmentAmazonRelationEntity::getFbaShipmentId, messageContent.getOmsShipmentBoxRelationDtoList().stream().map(OmsShipmentBoxRelationDto::getShipmentId).distinct().collect(Collectors.toList()));
        remove(wrapper);
        List<StockoutShipmentEntity> shipmentEntities = shipmentService.getBaseMapper().listByOrderNo(Collections.singletonList(erpTid));
        List<StockoutOrderEntity> orderEntity = orderItemService.getStockoutOrderEntityByOrderNo(messageContent.getErpTid());
        List<String> stockoutOrderNoList = orderEntity.stream().filter(order -> !StockoutOrderStatusEnum.CANCELLING.name().equals(order.getStatus()) && !StockoutOrderStatusEnum.CANCELLED.name().equals(order.getStatus()))
                .map(StockoutOrderEntity::getStockoutOrderNo).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stockoutOrderNoList)) {
            throw new BusinessServiceException("未找到对应的出库单");
        }
        if (CollectionUtils.isEmpty(shipmentEntities)) {
            LOGGER.info("未找到装箱清单，无法同步映射: {}", JsonMapper.toJson(messageContent));
            return;
        }
        Map<Integer, StockoutShipmentEntity> shipmentMap = shipmentEntities.stream()
                .collect(Collectors.toMap(
                        StockoutShipmentEntity::getBoxIndex,
                        Function.identity(),
                        (existing, replacement) -> {
                            throw new BusinessServiceException("存在重复的箱号序号:" + existing.getBoxIndex());
                        }));
        messageContent.getOmsShipmentBoxRelationDtoList().forEach(dto -> dto.getBoxIndexList().stream().distinct().forEach(boxIndex -> {
            StockoutShipmentAmazonRelationEntity entity = new StockoutShipmentAmazonRelationEntity();
            StockoutShipmentEntity shipmentEntity = shipmentMap.get(boxIndex);
            if (shipmentEntity == null) {
                LOGGER.error("找不到装箱清单，无法同步映射: {}", JsonMapper.toJson(messageContent));
                return;
            }
            StockoutShipmentErpPickingBoxEntity boxEntity = erpPickingBoxService.getTopByShipmentId(shipmentEntity.getShipmentId());
            if (boxEntity == null) {
                LOGGER.error("找不到wms对应的erp装箱: {} , 消息数据: {}", boxIndex, JsonMapper.toJson(messageContent));
                return;
            }
            shipmentEntity.setFbaLabelStatus(FbaLabelStatusEnum.COMPLETE.name());
            shipmentService.update(new UpdateWrapper<StockoutShipmentEntity>().lambda()
                    .set(StockoutShipmentEntity::getFbaLabelStatus, FbaLabelStatusEnum.COMPLETE.name())
                    .eq(StockoutShipmentEntity::getShipmentId, shipmentEntity.getShipmentId()));
            entity.setErpTid(erpTid);
            entity.setFbaShipmentId(dto.getShipmentId());
            entity.setShipmentId(shipmentEntity.getShipmentId());
            entity.setShipToAddressLine1(dto.getShipToAddressLine1());
            entity.setShipToCity(dto.getShipToCity());
            entity.setShipToName(dto.getShipToName());
            entity.setShipToCountryCode(dto.getShipToCountryCode());
            entity.setShipToPostalCode(dto.getShipToPostalCode());
            entity.setShipToStateOrProvinceCode(dto.getShipToStateOrProvinceCode());
            entity.setErpPickingBoxId(boxEntity.getErpPickingBoxId());
            entity.setAmazonReferenceId(dto.getAmazonRefrenceId());
            entity.setDestinationFulfillmentCenterId(dto.getDestinationFulfillmentCenterId());
            entity.setLocation(TenantContext.getTenant());
            entity.setCreateBy(dto.getCreateBy());
            entity.setUpdateBy(dto.getCreateBy());
            entity.setLabelUrl(dto.getLabelUrl());
            save(entity);
        }));
        orderService.update(new UpdateWrapper<StockoutOrderEntity>().lambda()
                .set(StockoutOrderEntity::getFbaLabelStatus, FbaLabelStatusEnum.COMPLETE.name())
                .in(StockoutOrderEntity::getStockoutOrderNo, stockoutOrderNoList));
    }
}
