package com.nsy.wms.business.service.stockout;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.domain.product.ProductInfo;
import com.nsy.api.wms.domain.product.ProductSpecInfo;
import com.nsy.api.wms.domain.stockout.StockoutReceiverInfo;
import com.nsy.api.wms.enumeration.PrintTemplateNameEnum;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderPlatformEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.request.stockout.StockoutOrderTikTokGetLabelRequest;
import com.nsy.api.wms.request.stockout.StringListRequest;
import com.nsy.api.wms.response.common.SauPlatformAuthConfigResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.tiktokshop.TikTokLogisticsPrintResponse;
import com.nsy.api.wms.response.tiktokshop.TikTokShopCreateOrderResponse;
import com.nsy.api.wms.response.tiktokshop.TikTokShopDeliveryOrderInfo;
import com.nsy.api.wms.response.tiktokshop.TikTokShopDeliveryOrderResponse;
import com.nsy.api.wms.response.tiktokshop.TikTokShopPrintResponse;
import com.nsy.api.wms.response.tiktokshop.TikTokShopStockUpOrderResponse;
import com.nsy.api.wms.response.tiktokshop.TikTokWarehouseContactInfo;
import com.nsy.wms.business.manage.thirdparty.OmsApiService;
import com.nsy.wms.business.manage.thirdparty.ThirdPartyApiService;
import com.nsy.wms.business.manage.thirdparty.request.PlatformAuthConfigRequest;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokAuth;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopCreateOrderDetail;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopCreateOrderPackageInfo;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopCreateOrderPackageItemInfo;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopCreateOrderRequest;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopDeliveryOrderPrintRequest;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopDeliveryOrderQueryRequest;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopGetLogisticsInfoRequest;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopReserveDateInfo;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopReserveLogisticsInfo;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopReserveSendInfo;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopReserveShipAddressInfo;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopReserveShipRequest;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopReserveShipWeightDetail;
import com.nsy.wms.business.manage.thirdparty.request.tiktokshp.TikTokShopStockUpOrderQueryRequest;
import com.nsy.wms.business.manage.thirdparty.response.ResponseTokenInfo;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.config.PrintTemplateService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.system.AliyunOssService;
import com.nsy.wms.controller.stockout.StockoutOrderTikTokReserveShipRequest;
import com.nsy.wms.repository.entity.bd.BdSystemParameterEntity;
import com.nsy.wms.repository.entity.config.PrintTemplateEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderStoreManufacturerMappingEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderTikTokExtendEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderTikTokExtendItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderTikTokExtendMapper;
import com.nsy.wms.utils.PrintTransferUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/17 15:05
 */
@Service
public class StockoutOrderTikTokExtendService extends ServiceImpl<StockoutOrderTikTokExtendMapper, StockoutOrderTikTokExtendEntity> {

    @Autowired
    OmsApiService omsApiService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    ThirdPartyApiService thirdPartyApiService;
    @Autowired
    StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    AliyunOssService aliyunOssService;
    @Autowired
    PrintTemplateService printService;
    @Autowired
    StockoutShipmentItemService shipmentItemService;
    @Autowired
    StockoutShipmentService shipmentService;
    @Autowired
    StockoutOrderTikTokExtendItemService tikTokExtendItemService;
    @Autowired
    BdSystemParameterService bdSystemParameterService;
    @Autowired
    StockoutShipmentItemService stockoutShipmentItemService;
    @Autowired
    StockoutReceiverInfoService stockoutReceiverInfoService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    StockoutOrderStoreManufacturerMappingService stockoutOrderStoreManufacturerMappingService;
    @Autowired
    private ProductInfoService productInfoService;


    public StockoutOrderTikTokExtendEntity findByStockoutOrderNo(String stockoutOrderNo) {
        return getOne(new LambdaQueryWrapper<StockoutOrderTikTokExtendEntity>()
                .eq(StockoutOrderTikTokExtendEntity::getStockoutOrderNo, stockoutOrderNo));
    }

    public PrintListResponse printLabelByShipmentList(StringListRequest request) {
        PrintListResponse printListResponse = new PrintListResponse();
        PrintTemplateEntity temp = printService.getByName("Tiktok条码(分隔)");
        request.getStringList().forEach(detail -> {
            PrintListResponse response = this.printLabelByShipment(detail);
            printListResponse.setSpec(response.getSpec());
            printListResponse.setTemplateName(response.getTemplateName());
            List<String> htmlList = response.getHtmlList();
            if (!CollectionUtils.isEmpty(printListResponse.getHtmlList())) {
                htmlList.add(temp.getContent());
                htmlList.addAll(response.getHtmlList());
            }
            printListResponse.setHtmlList(htmlList);
        });
        return printListResponse;
    }

    /**
     * 按装箱清单号打印
     *
     * @param shipmentBoxCode
     * @return
     */
    public PrintListResponse printLabelByShipment(String shipmentBoxCode) {
        StockoutShipmentEntity shipment = shipmentService.findTopByShipmentBoxCode(shipmentBoxCode);
        List<StockoutShipmentItemEntity> shipmentItemList = shipmentItemService.findByShipmentId(shipment.getShipmentId());
        if (shipmentItemList.isEmpty())
            throw new BusinessServiceException("装箱清单明细为空");
        List<StockoutOrderTikTokGetLabelRequest.StockoutOrderTikTokGetLabelRequestItem> itemList = shipmentItemList.stream().map(shipmentItem -> {
            StockoutOrderTikTokGetLabelRequest.StockoutOrderTikTokGetLabelRequestItem item = new StockoutOrderTikTokGetLabelRequest.StockoutOrderTikTokGetLabelRequestItem();
            item.setSku(shipmentItem.getSku());
            item.setQty(shipmentItem.getQty());
            return item;
        }).collect(Collectors.toList());
        StockoutOrderTikTokGetLabelRequest request = new StockoutOrderTikTokGetLabelRequest();
        request.setStockoutOrderNo(shipmentItemList.get(0).getStockoutOrderNo());
        request.setItemList(itemList);
        return printLabelBySku(request);
    }

    /**
     * 按出库单号打印
     *
     * @param stockoutOrderNo
     * @return
     */
    public PrintListResponse printLabelByStockoutOrderNo(String stockoutOrderNo) {
        List<StockoutOrderTikTokExtendItemEntity> itemList = tikTokExtendItemService.listByStockoutOrderNo(stockoutOrderNo);
        if (itemList.isEmpty())
            throw new BusinessServiceException("明细为空");
        List<StockoutOrderTikTokGetLabelRequest.StockoutOrderTikTokGetLabelRequestItem> printItemList = itemList.stream().map(detail -> {
            StockoutOrderTikTokGetLabelRequest.StockoutOrderTikTokGetLabelRequestItem item = new StockoutOrderTikTokGetLabelRequest.StockoutOrderTikTokGetLabelRequestItem();
            item.setSku(detail.getSku());
            item.setQty(detail.getQty());
            return item;
        }).collect(Collectors.toList());
        StockoutOrderTikTokGetLabelRequest request = new StockoutOrderTikTokGetLabelRequest();
        request.setStockoutOrderNo(stockoutOrderNo);
        request.setItemList(printItemList);
        return printLabelBySku(request);
    }

    /**
     * 按sku打印
     *
     * @param request
     * @return
     */
    public PrintListResponse printLabelBySku(StockoutOrderTikTokGetLabelRequest request) {
        PrintTemplateEntity templateEntity = printService.getByName(PrintTemplateNameEnum.TIKTOK_PRODUCT_CUSTOMER_SPEC_BARCODE.getTemplateName());
        PrintListResponse response = new PrintListResponse();
        response.setHtmlList(new ArrayList<>());
        response.setTemplateName(templateEntity.getName());
        response.setSpec(templateEntity.getSpec());
        StockoutOrderTikTokExtendEntity extend = findByStockoutOrderNo(request.getStockoutOrderNo());
        if (Objects.isNull(extend))
            throw new BusinessServiceException(String.format("%s 为导入订单，请到TikTok平台打印", request.getStockoutOrderNo()));

        List<StockoutOrderTikTokExtendItemEntity> tiktokExtendItemList = tikTokExtendItemService.findByStockoutOrderNoAndSku(request.getStockoutOrderNo(), request.getItemList().stream()
                .map(StockoutOrderTikTokGetLabelRequest.StockoutOrderTikTokGetLabelRequestItem::getSku).collect(Collectors.toList()));

        PrintTemplateEntity temp = printService.getByName("Tiktok条码(分隔)");
        String tempSku = null;
        for (StockoutOrderTikTokGetLabelRequest.StockoutOrderTikTokGetLabelRequestItem printDetail : request.getItemList()) {
            if (tempSku != null && !printDetail.getSku().equals(tempSku)) {
                response.getHtmlList().add(temp.getContent());
            }
            tempSku = printDetail.getSku();
            StockoutOrderTikTokExtendItemEntity itemInfo = tiktokExtendItemList.stream().filter(detail -> detail.getSku().equalsIgnoreCase(printDetail.getSku())).findFirst().orElse(null);
            ProductSpecInfoEntity skuInfo = productSpecInfoService.findTopBySku(itemInfo.getSku());
            String transfer = PrintTransferUtils.transfer(templateEntity.getContent(), itemInfo);
            Map<String, String> map = new HashMap<>();
            map.put("orderNo", extend.getOrderNo());
            map.put("sellerSku", itemInfo.getSellerSku());
            map.put("sku", itemInfo.getSku());
            map.put("sellerBarcode", itemInfo.getSellerSku());
            if (Objects.nonNull(skuInfo)) {
                map.put("size", skuInfo.getSize());
            }
            map.put("color", itemInfo.getColor());

            String result = PrintTransferUtils.transfer(transfer, map);
            for (int i = 0; i < printDetail.getQty(); i++)
                response.getHtmlList().add(result);
        }
        return response;
    }

    /**
     * 备货单信息更新
     *
     * @param stockoutOrderNo
     */
    public void updateStockUpOrderInfo(String stockoutOrderNo) {
        StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
        if (!StockoutOrderPlatformEnum.TIKTOK_SHOP.getName().equals(stockoutOrder.getPlatformName()))
            throw new BusinessServiceException(String.format("%s 非TitTok全托管订单", stockoutOrderNo));
        StockoutOrderTikTokExtendEntity tiktokExtendEntity = this.findByStockoutOrderNo(stockoutOrderNo);
        if (Objects.isNull(tiktokExtendEntity)) {
            throw new BusinessServiceException(String.format("找不到扩展表 %s", stockoutOrder.getStockoutOrderNo()));
        }
        List<StockoutOrderTikTokExtendItemEntity> tokExtendItemEntities = tikTokExtendItemService.listByStockoutOrderNo(tiktokExtendEntity.getStockoutOrderNo());
        //颜色都已获取到则不在处理
        if (tokExtendItemEntities.stream().allMatch(item -> StringUtils.isNoneBlank(item.getColor()))) {
            return;
        }
        if (StringUtils.isBlank(tiktokExtendEntity.getOrderNo())) {
            throw new BusinessServiceException(String.format("当前订单号不存在请检查 %s", stockoutOrder.getStockoutOrderNo()));
        }
        TikTokShopStockUpOrderQueryRequest request = new TikTokShopStockUpOrderQueryRequest();
        request.setStockUpOrderList(Collections.singletonList(tiktokExtendEntity.getOrderNo()));
        request.setTiktokAuth(this.getTikTokAuth(stockoutOrder.getStoreId(), stockoutOrder.getStoreName()));
        TikTokShopStockUpOrderResponse tiktokResponse = thirdPartyApiService.stockUpOrderQuery(request);
        if (Objects.isNull(tiktokResponse)) {
            throw new BusinessServiceException("当前订单查询不到备货单,请检查!");
        }
        tokExtendItemEntities.stream().forEach(detail -> {
            detail.setColor(tiktokResponse.getSkc().getFirstKeyAttributeValueZh());
        });
        tikTokExtendItemService.updateBatchById(tokExtendItemEntities);

    }

    public void creatDeliveryOrder(List<String> stockoutOrderNoList) {
        List<StockoutOrderEntity> orderNoList = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNoList);
        if (CollectionUtils.isEmpty(orderNoList)) {
            throw new BusinessServiceException("出库单不存在请检查!");
        }
        //校验信息
        for (StockoutOrderEntity detail : orderNoList) {
            if (!StockoutOrderStatusEnum.READY_DELIVERY.name().equals(detail.getStatus()))
                throw new BusinessServiceException(String.format("%s 非待发货", detail.getStockoutOrderNo()));
            if (!StockoutOrderPlatformEnum.TIKTOK_SHOP.getName().equals(detail.getPlatformName()))
                throw new BusinessServiceException(String.format("%s 非TitTok全托管订单", detail.getStockoutOrderNo()));

            StockoutOrderTikTokExtendEntity tiktokExtendEntity = this.findByStockoutOrderNo(detail.getStockoutOrderNo());
            if (Objects.isNull(tiktokExtendEntity))
                throw new BusinessServiceException(String.format("%s 为导入订单，请到平台打印", detail.getStockoutOrderNo()));
            if (StrUtil.isNotEmpty(tiktokExtendEntity.getDeliveryCode()))
                throw new BusinessServiceException(String.format("%s 已创建发货单", detail.getStockoutOrderNo()));
        }
        TikTokAuth tikTokAuth = this.getTikTokAuth(orderNoList.get(0).getStoreId(), orderNoList.get(0).getStoreName());
        for (StockoutOrderEntity detail : orderNoList) {
            TikTokShopCreateOrderRequest request = new TikTokShopCreateOrderRequest();
            //获取鉴权信息
            request.setTiktokAuth(tikTokAuth);
            StockoutOrderTikTokExtendEntity tiktokExtendEntity = this.findByStockoutOrderNo(detail.getStockoutOrderNo());
            //获取发货基本信息
            TikTokShopCreateOrderDetail createOrderRequest = new TikTokShopCreateOrderDetail();
            createOrderRequest.setStockupOrderCode(tiktokExtendEntity.getOrderNo());
            createOrderRequest.setPackageQuantity(1);
            List<StockoutOrderItemEntity> stockoutOrderItemList = stockoutOrderItemService.getListByStockoutOrderNo(detail.getStockoutOrderNo());
            Map<String, Integer> shipmentMap = stockoutOrderItemList.stream().collect(Collectors.groupingBy(StockoutOrderItemEntity::getSellerSku, Collectors.summingInt(StockoutOrderItemEntity::getShipmentQty)));
            List<TikTokShopCreateOrderPackageItemInfo> packages = new ArrayList<>(shipmentMap.size());
            for (Map.Entry<String, Integer> entry : shipmentMap.entrySet()) {
                TikTokShopCreateOrderPackageItemInfo packageDetail = new TikTokShopCreateOrderPackageItemInfo();
                packageDetail.setPlatformSkuCode(entry.getKey());
                packageDetail.setQuantity(entry.getValue());
                packages.add(packageDetail);
            }
            TikTokShopCreateOrderPackageInfo packageInfo = new TikTokShopCreateOrderPackageInfo();
            packageInfo.setItems(packages);
            createOrderRequest.setPackages(Collections.singletonList(packageInfo));
            request.setDeliveryOrder(createOrderRequest);
            //发货
            TikTokShopCreateOrderResponse tiktokResponse = thirdPartyApiService.createTikTokDeliveryOrder(request);
            tiktokExtendEntity.setDeliveryCode(tiktokResponse.getDeliveryOrderCode());
            tiktokExtendEntity.setDeliveryCreateDate(new Date());
            this.updateById(tiktokExtendEntity);
            stockoutOrderLogService.addLog(detail.getStockoutOrderNo(), StockoutOrderLogTypeEnum.SHEIN_CREATE_SHIPORDER, String.format("TikTok全托管创建送货单，送货单号【%s】", tiktokExtendEntity.getDeliveryCode()));
            this.updateReviceInfo(tiktokExtendEntity, request.getTiktokAuth());
        }
    }

    private void updateReviceInfo(StockoutOrderTikTokExtendEntity tiktokExtendEntity, TikTokAuth tiktokAuth) {
        TikTokShopDeliveryOrderQueryRequest deliveryOrderRequest = new TikTokShopDeliveryOrderQueryRequest();
        deliveryOrderRequest.setDeliveryOrderCodes(Collections.singletonList(tiktokExtendEntity.getDeliveryCode()));
        deliveryOrderRequest.setTiktokAuth(tiktokAuth);
        TikTokShopDeliveryOrderResponse deliveryOrder = thirdPartyApiService.deliveryOrderQuery(deliveryOrderRequest);
        if (CollectionUtils.isEmpty(deliveryOrder.getDeliveryOrders()) || Objects.isNull(deliveryOrder.getDeliveryOrders().get(0).getWarehouseContact())) {
            return;
        }
        TikTokWarehouseContactInfo warehouseContact = deliveryOrder.getDeliveryOrders().get(0).getWarehouseContact();
        StockoutReceiverInfo receiverInfo = new StockoutReceiverInfo();
        receiverInfo.setReceiverPhone(warehouseContact.getPhoneNumber());
        receiverInfo.setReceiverName(warehouseContact.getContactName());
        receiverInfo.setReceiverZip(warehouseContact.getPostalCode());
        receiverInfo.setReceiverAddress(warehouseContact.getFullAddress());
        receiverInfo.setReceiverCountry(warehouseContact.getAddressDetail().getCountryName());
        receiverInfo.setReceiverCity(warehouseContact.getAddressDetail().getCityName());
        receiverInfo.setReceiverDistrict(warehouseContact.getAddressDetail().getDistrictName());
        receiverInfo.setReceiverState(warehouseContact.getAddressDetail().getTownName());
        stockoutReceiverInfoService.updateInfo(receiverInfo, tiktokExtendEntity.getStockoutOrderId());
    }

    public PrintListResponse printDeliveryOrder(List<String> stockoutOrderNoList) {
        List<StockoutOrderEntity> orderNoList = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNoList);
        if (CollectionUtils.isEmpty(orderNoList)) {
            throw new BusinessServiceException("出库单不存在请检查!");
        }
        List<String> printUrlList = new ArrayList<>();
        for (StockoutOrderEntity detail : orderNoList) {
            StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderNo(detail.getStockoutOrderNo());
            if (!StockoutOrderPlatformEnum.TIKTOK_SHOP.getName().equals(stockoutOrder.getPlatformName()))
                throw new BusinessServiceException(String.format("%s 非TitTok全托管订单", detail.getStockoutOrderNo()));

            StockoutOrderTikTokExtendEntity tiktokExtendEntity = this.findByStockoutOrderNo(detail.getStockoutOrderNo());
            if (Objects.isNull(tiktokExtendEntity))
                throw new BusinessServiceException(String.format("%s 为导入订单，请到平台打印", detail.getStockoutOrderNo()));
            if (StrUtil.isEmpty(tiktokExtendEntity.getDeliveryCode()))
                throw new BusinessServiceException(String.format("%s 未创建送货单", detail.getStockoutOrderNo()));
            if (StringUtils.isNoneBlank(tiktokExtendEntity.getDeliveryLabelUrl())) {
                printUrlList.add(tiktokExtendEntity.getDeliveryLabelUrl());
                continue;
            }
            TikTokShopDeliveryOrderPrintRequest request = new TikTokShopDeliveryOrderPrintRequest();
            request.setDeliveryNoList(Collections.singletonList(tiktokExtendEntity.getDeliveryCode()));
            request.setTiktokAuth(this.getTikTokAuth(stockoutOrder.getStoreId(), stockoutOrder.getStoreName()));
            String fileUrl = this.uploadAliOss(thirdPartyApiService.printTikTokDeliveryOrder(request).getDocumentUrl(), tiktokExtendEntity.getDeliveryCode());
            tiktokExtendEntity.setDeliveryLabelUrl(fileUrl);
            this.updateById(tiktokExtendEntity);
            printUrlList.add(fileUrl);
        }
        return this.printByUrl(printUrlList, PrintTemplateNameEnum.TIKTOK_DELIVERY_LABEL.getTemplateName());
    }

    public TikTokLogisticsPrintResponse printLogisticsLabel(List<String> stockoutOrderNoList) {
        List<StockoutOrderEntity> orderNoList = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNoList);
        if (CollectionUtils.isEmpty(orderNoList)) {
            throw new BusinessServiceException("出库单不存在请检查!");
        }
        for (StockoutOrderEntity detail : orderNoList) {
            if (!StockoutOrderPlatformEnum.TIKTOK_SHOP.getName().equals(detail.getPlatformName()))
                throw new BusinessServiceException(String.format("%s 非TitTok全托管订单", detail.getStockoutOrderNo()));

            StockoutOrderTikTokExtendEntity tiktokExtendEntity = this.findByStockoutOrderNo(detail.getStockoutOrderNo());
            if (StrUtil.isEmpty(tiktokExtendEntity.getDeliveryBatchCode()))
                throw new BusinessServiceException(String.format("%s 订单未发货,请先预约发货!", detail.getStockoutOrderNo()));
            if (Objects.isNull(tiktokExtendEntity))
                throw new BusinessServiceException(String.format("%s 为导入订单，请到平台打印", detail.getStockoutOrderNo()));

        }
        int count = this.getBaseMapper().getDeliveryCountByStockoutOrderNoList(stockoutOrderNoList);
        if (count > 1) {
            throw new BusinessServiceException("请选择同一批次出库单进行打印!");
        }
        TikTokLogisticsPrintResponse printResponse = new TikTokLogisticsPrintResponse();
        StockoutOrderTikTokExtendEntity tiktokExtendEntity = this.findByStockoutOrderNo(stockoutOrderNoList.get(0));
        if (StringUtils.isNoneBlank(tiktokExtendEntity.getLogisticsLabelUrl())) {
            printResponse.setPrintList(this.printByUrl(Collections.singletonList(tiktokExtendEntity.getLogisticsLabelUrl()), PrintTemplateNameEnum.TIKTOK_LOGISTICS_LABEL.getTemplateName()));
            printResponse.setLogisticsNo(tiktokExtendEntity.getExpressCode());
            return printResponse;
        }
        TikTokShopGetLogisticsInfoRequest request = new TikTokShopGetLogisticsInfoRequest();
        request.setDeliveryBatchCodes(Collections.singletonList(tiktokExtendEntity.getDeliveryBatchCode()));
        request.setTiktokAuth(this.getTikTokAuth(orderNoList.get(0).getStoreId(), orderNoList.get(0).getStoreName()));
        TikTokShopPrintResponse tikTokShopPrintResponse = thirdPartyApiService.printLogisticsLabel(request);
        String fileUrl = this.uploadAliOss(tikTokShopPrintResponse.getDocumentUrl(), tiktokExtendEntity.getDeliveryBatchCode());
        tikTokShopPrintResponse.setDocumentUrl(fileUrl);
        tiktokExtendEntity.setLogisticsLabelUrl(fileUrl);
        tiktokExtendEntity.setExpressCode(tikTokShopPrintResponse.getLogisticsNo());
        this.update(new LambdaUpdateWrapper<StockoutOrderTikTokExtendEntity>()
                .set(StockoutOrderTikTokExtendEntity::getLogisticsLabelUrl, fileUrl)
                .set(StockoutOrderTikTokExtendEntity::getExpressCode, tikTokShopPrintResponse.getLogisticsNo())
                .in(StockoutOrderTikTokExtendEntity::getStockoutOrderNo, stockoutOrderNoList));
        printResponse.setPrintList(this.printByUrl(Collections.singletonList(tiktokExtendEntity.getLogisticsLabelUrl()), PrintTemplateNameEnum.TIKTOK_LOGISTICS_LABEL.getTemplateName()));
        printResponse.setLogisticsNo(tiktokExtendEntity.getExpressCode());
        return printResponse;
    }

    public void reserveShip(StockoutOrderTikTokReserveShipRequest request) {
        List<String> stockoutOrderNoList = request.getStringList();
        List<StockoutOrderEntity> orderNoList = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNoList);
        if (CollectionUtils.isEmpty(orderNoList)) {
            throw new BusinessServiceException("出库单不存在请检查!");
        }
        List<String> deliveryOrderList = new ArrayList<>(orderNoList.size());
        BigDecimal totalWeight = BigDecimal.ZERO;
        for (StockoutOrderEntity detail : orderNoList) {
            if (!StockoutOrderPlatformEnum.TIKTOK_SHOP.getName().equals(detail.getPlatformName())) {
                throw new BusinessServiceException(String.format("%s 非TitTok全托管订单", detail.getStockoutOrderNo()));
            }
            StockoutOrderTikTokExtendEntity tiktokExtendEntity = this.findByStockoutOrderNo(detail.getStockoutOrderNo());
            if (Objects.isNull(tiktokExtendEntity))
                throw new BusinessServiceException(String.format("%s 为导入订单，请到平台打印", detail.getStockoutOrderNo()));
            if (StrUtil.isEmpty(tiktokExtendEntity.getDeliveryCode()))
                throw new BusinessServiceException(String.format("%s 订单送货单未创建,请先创建送货单!", detail.getStockoutOrderNo()));
            if (StrUtil.isNotEmpty(tiktokExtendEntity.getDeliveryBatchCode()))
                throw new BusinessServiceException(String.format("%s 已预约发货", detail.getStockoutOrderNo()));
            List<StockoutShipmentEntity> shipmentEntityList = stockoutShipmentItemService.findByStockoutOrderNo(detail.getStockoutOrderNo());
            if (shipmentEntityList.stream().anyMatch(shipmentInfo -> Objects.isNull(shipmentInfo.getWeight()) || BigDecimal.ZERO.compareTo(shipmentInfo.getWeight()) == 0)) {
                throw new BusinessServiceException("当前装箱清单未填写重量，请确认!");
            }
            totalWeight = totalWeight.add(shipmentEntityList.stream().distinct().map(StockoutShipmentEntity::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add));
            deliveryOrderList.add(tiktokExtendEntity.getDeliveryCode());
        }
        TikTokShopReserveShipWeightDetail weightDetail = new TikTokShopReserveShipWeightDetail();
        weightDetail.setUnit("KILOGRAM");
        weightDetail.setValue(String.valueOf(totalWeight.setScale(2, RoundingMode.HALF_UP)));
        TikTokShopDeliveryOrderQueryRequest deliveryOrderRequest = new TikTokShopDeliveryOrderQueryRequest();
        TikTokAuth tikTokAuth = this.getTikTokAuth(orderNoList.get(0).getStoreId(), orderNoList.get(0).getStoreName());
        deliveryOrderRequest.setDeliveryOrderCodes(deliveryOrderList);
        deliveryOrderRequest.setTiktokAuth(tikTokAuth);
        TikTokShopDeliveryOrderResponse deliveryOrder = thirdPartyApiService.deliveryOrderQuery(deliveryOrderRequest);
        if (CollectionUtils.isEmpty(deliveryOrder.getDeliveryOrders())) {
            throw new BusinessServiceException("送货单生成中,请稍后!");
        }
        TikTokShopReserveShipRequest reserveShipRequest = new TikTokShopReserveShipRequest();
        this.bulidReserveShipRequestInfo(deliveryOrder.getDeliveryOrders().get(0), tikTokAuth, reserveShipRequest, request);
        reserveShipRequest.setTotalWeight(weightDetail);
        String deliveryBatchCode = thirdPartyApiService.reserveShip(reserveShipRequest).getDeliveryBatchCode();
        this.update(new LambdaUpdateWrapper<StockoutOrderTikTokExtendEntity>()
                .set(StockoutOrderTikTokExtendEntity::getDeliveryBatchCode, deliveryBatchCode)
                .set(StockoutOrderTikTokExtendEntity::getSupplierWarehouseCode, deliveryOrder.getDeliveryOrders().get(0).getWarehouseCode())
                .in(StockoutOrderTikTokExtendEntity::getStockoutOrderNo, stockoutOrderNoList));
    }

    /**
     * 组织请求参数，由于指定了物流为顺丰则不咋调用接口查询可以预约时间了
     *
     * @param deliveryOrder
     * @param tikTokAuth
     * @param reserveShipRequest
     */
    public void bulidReserveShipRequestInfo(TikTokShopDeliveryOrderInfo deliveryOrder, TikTokAuth tikTokAuth, TikTokShopReserveShipRequest reserveShipRequest,
                                            StockoutOrderTikTokReserveShipRequest request) {
        reserveShipRequest.setReserve(this.buildLogisticsAppointment(request));
        reserveShipRequest.setWarehouseCode(deliveryOrder.getWarehouseCode());
        reserveShipRequest.setDeliveryMode("PLATFORM_DELIVERY");
        reserveShipRequest.setDeliveryOption("JIT".equalsIgnoreCase(deliveryOrder.getDeliveryType()) ? "SUPER_SPEEDY_EXPRESS" : "STANDARD_EXPRESS");
        reserveShipRequest.setShippingBoxQuantity(1);
        reserveShipRequest.setSenderContact(this.buildSenderContactInfo());
        reserveShipRequest.setDeliveryOrderCodes(Collections.singletonList(deliveryOrder.getCode()));
        reserveShipRequest.setLogistics(this.buildLogisticsInfo(reserveShipRequest.getDeliveryOption()));
        reserveShipRequest.setTiktokAuth(tikTokAuth);
    }

    /**
     * 获取店铺接口权限
     *
     * @param storeId
     * @return
     */
    public TikTokAuth getTikTokAuth(Integer storeId, String storeName) {
        SauPlatformAuthConfigResponse sauPlatformAuthConfigResponse = omsApiService.getPlatformConfigByStoreId(storeId);
        if (Objects.isNull(sauPlatformAuthConfigResponse)) {
            throw new BusinessServiceException(String.format("店铺【%s】没有鉴权", storeName));
        }
        TikTokAuth tiktokAuth = new TikTokAuth();
        tiktokAuth.setShopCipher(sauPlatformAuthConfigResponse.getAccountProperties5());
        tiktokAuth.setAccessToken(sauPlatformAuthConfigResponse.getAccountProperties1());
        tiktokAuth.setAccessTokenExpireIn(sauPlatformAuthConfigResponse.getAccountProperties3());
        tiktokAuth.setRefreshToken(sauPlatformAuthConfigResponse.getAccountProperties2());
        tiktokAuth.setAppKey(sauPlatformAuthConfigResponse.getAppKey());
        tiktokAuth.setAppSecret(sauPlatformAuthConfigResponse.getAppSecret());
        tiktokAuth.setOpenId(sauPlatformAuthConfigResponse.getAccountProperties4());
        return tiktokAuth;
    }

    public TikTokAuth getTikTokAuth(Integer storeId) {
        return getTikTokAuth(storeId, storeId.toString());
    }

    public void saveRefreshToken(ResponseTokenInfo responseTokenInfo, Integer storeId) {
        if (Objects.isNull(responseTokenInfo)) {
            return;
        }
        PlatformAuthConfigRequest updateStoreAuthInfoRequest = new PlatformAuthConfigRequest();
        updateStoreAuthInfoRequest.setStoreId(storeId);
        updateStoreAuthInfoRequest.setAccountProperties1(responseTokenInfo.getAccessToken());
        updateStoreAuthInfoRequest.setAccountProperties2(responseTokenInfo.getRefreshToken());
        updateStoreAuthInfoRequest.setAccountProperties3(responseTokenInfo.getAccessTokenTimeOut());
        omsApiService.updateStoreAuthInfo(updateStoreAuthInfoRequest);

    }

    /**
     * 获取labelUrl
     *
     * @param url
     * @param fileNamePrefix
     */
    public String uploadAliOss(String url, String fileNamePrefix) {
        byte[] bytes = HttpUtil.downloadBytes(url);
        //上传阿里
        return aliyunOssService.putObject(IoUtil.toStream(bytes), "tiktokShopLabel", String.format("%s.pdf", fileNamePrefix));
    }

    private TikTokShopReserveLogisticsInfo buildLogisticsInfo(String deliveryOption) {
        BdSystemParameterEntity bdSystemParameterEntity = bdSystemParameterService.getByKey(BdSystemParameterEnum.WMS_TIKTOK_DESIGNATED_LOGISTICS.getKey());
        if (Objects.isNull(bdSystemParameterEntity)) {
            throw new BusinessServiceException("请配置可以指定的物流信息!");
        }
        //指定的物流信息 value示例为  code,name 如 7350150641810933506,顺丰大网  7233389346693318402,跨越速运  code、name需要调用tk可预约物流接口查询
        List<String> appointTimeList = Arrays.asList(bdSystemParameterEntity.getConfigValue().split(","));
        TikTokShopReserveLogisticsInfo logistics = new TikTokShopReserveLogisticsInfo();
        logistics.setShippingProviderCode(appointTimeList.get(0));
        logistics.setShippingProviderName(appointTimeList.get(1));
        logistics.setDeliveryOption(deliveryOption);
        return logistics;
    }

    /**
     * 获取物流预约时间
     *
     * @return
     */
    private TikTokShopReserveDateInfo buildLogisticsAppointment(StockoutOrderTikTokReserveShipRequest request) {
        TikTokShopReserveDateInfo reserveDateInfo = new TikTokShopReserveDateInfo();
        // 获取今天的日期
        LocalDate today = LocalDate.now();
        //  物流预约开始时间
        String dateTimeBeginString = today.toString() + "T" + request.getAppointmentStart();
        //  物流预约结束时间
        String dateTimeEndString = today.toString() + "T" + request.getAppointmentEnd();
        // 将日期时间字符串转换为 LocalDateTime 对象
        reserveDateInfo.setPredictedPickupGe((int) LocalDateTime.parse(dateTimeBeginString, DateTimeFormatter.ISO_LOCAL_DATE_TIME).atZone(ZoneId.systemDefault()).toEpochSecond());
        // 将日期时间字符串转换为 LocalDateTime 对象
        reserveDateInfo.setPredictedPickupLt((int) LocalDateTime.parse(dateTimeEndString, DateTimeFormatter.ISO_LOCAL_DATE_TIME).atZone(ZoneId.systemDefault()).toEpochSecond());
        // 将时间设置为当天的午夜 00:00
        LocalDateTime midnight = LocalDateTime.of(today, LocalTime.MIDNIGHT);
        //当天时间时间戳
        reserveDateInfo.setPredictedPickupTime((int) midnight.atZone(ZoneId.systemDefault()).toEpochSecond());
        // 转换为时间戳（秒数）
        return reserveDateInfo;
    }

    private TikTokShopReserveSendInfo buildSenderContactInfo() {
        TikTokShopReserveSendInfo sendInfo = new TikTokShopReserveSendInfo();
        sendInfo.setFullAddress("福建省泉州市惠安县东园镇惠南工业区山紫阳村贵人鸟股份有限公司进门右拐6号仓-时颖服饰");
        sendInfo.setContactName("郑宏星转王诗婷");
        sendInfo.setPhoneNumber("+8618060067595");
        sendInfo.setPostalCode("362122");
        sendInfo.setEmail("<EMAIL>");
        TikTokShopReserveShipAddressInfo addressDetail = new TikTokShopReserveShipAddressInfo();
        addressDetail.setCountryId(1814991);
        addressDetail.setCountryName("中华人民共和国");
        addressDetail.setProvinceId(1811017);
        addressDetail.setProvinceName("福建省");
        addressDetail.setCityId(1797353);
        addressDetail.setCityName("泉州市");
        addressDetail.setDistrictId(1806894);
        addressDetail.setDistrictName("惠安县");
        addressDetail.setTownId(350521105);
        addressDetail.setTownName("东园镇");
        addressDetail.setDetail("惠南工业区阳光山前滨海路西200米-盛时产业园");
        sendInfo.setAddressDetail(addressDetail);
        return sendInfo;
    }

    public PrintListResponse printByUrl(List<String> labelUrlList, String labelName) {
        PrintListResponse resp = new PrintListResponse();
        PrintTemplateEntity templateEntity = printService.getByName(labelName);
        List<String> htmlList = new ArrayList<>();
        labelUrlList.forEach(labelUrl -> {
            Map<String, String> map = new HashMap<>();
            map.put("printUrl", labelUrl);
            String transfer = PrintTransferUtils.transfer(templateEntity.getContent(), map);
            htmlList.add(transfer);
        });
        resp.setHtmlList(htmlList);
        resp.setSpec(templateEntity.getSpec());
        resp.setTemplateName(templateEntity.getName());
        return resp;
    }

    public PrintListResponse printEuLabel(String shipmentBoxCode) {
        StockoutShipmentEntity shipment = shipmentService.findTopByShipmentBoxCode(shipmentBoxCode);
        List<StockoutShipmentItemEntity> shipmentItemList = shipmentItemService.findByShipmentId(shipment.getShipmentId());
        if (shipmentItemList.isEmpty())
            throw new BusinessServiceException("装箱清单明细为空");
        StockoutShipmentItemEntity firstShipmentItem = shipmentItemList.get(0);
        StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderNo(firstShipmentItem.getStockoutOrderNo());
        StockoutOrderStoreManufacturerMappingEntity tiktokStore = stockoutOrderStoreManufacturerMappingService.findByStoreId(stockoutOrder.getStoreId(), StockoutOrderPlatformEnum.TIKTOK_SHOP.getName());
        if (Objects.isNull(tiktokStore)) {
            throw new BusinessServiceException(String.format("TK欧代店铺信息未配置 %s", stockoutOrder.getStoreName()));
        }

        PrintTemplateEntity templateEntity = printService.getByName(PrintTemplateNameEnum.TIKTOK_EU_LABEL.getTemplateName());

        List<String> htmlList = shipmentItemList.stream().map(shipmentItem -> {
            ProductSpecInfo specInfo = productSpecInfoService.getBySku(shipmentItem.getSku());
            ProductInfo productInfo = productInfoService.getByProductId(specInfo.getProductId());

            Map<String, String> map = new HashMap<>();
            map.put("manufacturerName", tiktokStore.getManufacturerName());
            map.put("manufacturerAddress", tiktokStore.getManufacturerAddress());
            map.put("manufacturerEmail", tiktokStore.getManufacturerEmail());
            map.put("batchNumber", productInfo.getSpu());
            // 添加欧盟责任人信息
            map.put("euResponsibleName", tiktokStore.getEuResponsibleName());
            map.put("euResponsibleAddress", tiktokStore.getEuResponsibleAddress());
            map.put("euResponsibleEmail", tiktokStore.getEuResponsibleEmail());
            map.put("euResponsiblePostcode", tiktokStore.getEuResponsiblePostcode());

            String html = PrintTransferUtils.transfer(templateEntity.getContent(), map);
            List<String> tempHtmlList = new ArrayList<>(shipmentItem.getQty());
            for (int i = 0; i < shipmentItem.getQty(); i++) {
                tempHtmlList.add(html);
            }
            return tempHtmlList;
        }).flatMap(Collection::stream).collect(Collectors.toList());

        PrintListResponse response = new PrintListResponse();
        response.setHtmlList(htmlList);
        response.setTemplateName(templateEntity.getName());
        response.setSpec(templateEntity.getSpec());
        return response;
    }
}
