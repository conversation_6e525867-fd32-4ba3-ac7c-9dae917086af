package com.nsy.wms.business.manage.user.upload;

public class StockTakeStockTaskImport {

    /**
     * 任务序号
     */
    private Integer index;

    /**
     * 盘点方式
     */
    private String taskGenerateMode;

    /**
     * 仓库
     */
    private String spaceName;

    /**
     * 盘点区域
     */
    private String areaName;

    /**
     * 库区
     */
    private String spaceAreaName;

    /**
     * 库位
     */
    private String positionCode;

    /**
     * 错误信息
     */
    private String errorMsg;

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getTaskGenerateMode() {
        return taskGenerateMode;
    }

    public void setTaskGenerateMode(String taskGenerateMode) {
        this.taskGenerateMode = taskGenerateMode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getSpaceAreaName() {
        return spaceAreaName;
    }

    public void setSpaceAreaName(String spaceAreaName) {
        this.spaceAreaName = spaceAreaName;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }
}
