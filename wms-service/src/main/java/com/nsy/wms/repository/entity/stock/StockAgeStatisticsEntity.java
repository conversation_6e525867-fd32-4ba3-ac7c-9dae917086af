package com.nsy.wms.repository.entity.stock;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "stock_age_statistics")
@TableName("stock_age_statistics")
public class StockAgeStatisticsEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer stockAgeId;

    /**
     * 地区
     */
    private String location;

    /**
     * 仓库id
     */
    private Integer spaceId;

    /**
     * 仓库
     */
    private String spaceName;

    /**
     * 商品id
     */
    private Integer productId;

    /**
     * 规格编码id
     */
    private Integer specId;

    /**
     * sku
     */
    private String sku;

    /**
     * 现有库存最早入库日期
     */
    private Date earliestInDate;

    /**
     * 现有库存最新出库日期
     */
    private Date latestOutDate;

    public Integer getStockAgeId() {
        return stockAgeId;
    }

    public void setStockAgeId(Integer stockAgeId) {
        this.stockAgeId = stockAgeId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Date getEarliestInDate() {
        return earliestInDate;
    }

    public void setEarliestInDate(Date earliestInDate) {
        this.earliestInDate = earliestInDate;
    }

    public Date getLatestOutDate() {
        return latestOutDate;
    }

    public void setLatestOutDate(Date latestOutDate) {
        this.latestOutDate = latestOutDate;
    }
}
