package com.nsy.wms.repository.entity.stockout;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * tk退货表
 */
@Entity
@Table(name = "stockout_return_product_tiktok")
@TableName("stockout_return_product_tiktok")
public class StockoutReturnProductTikTokEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String location;
    /**
     * 店铺id
     */
    private Integer storeId;

    /**
     * 退货单编号
     */
    private String code;

    /**
     * 退货单状态
     */
    private String status;

    /**
     * 退货仓库
     */
    private String warehouseCode;

    /**
     * 退货方式
     */
    private String method;

    /**
     * 退货单类型
     */
    private String type;

    /**
     * 退货来源
     */
    private String source;

    /**
     * 退货原因
     */
    private String reasonType;

    /**
     * 退货具体原因
     */
    private String detailedReason;

    /**
     * 关联单号，仓库出库批次编号
     */
    private String outboundBatchCode;

    /**
     * 物流服务商Code
     */
    private String shippingProviderCode;

    /**
     * 发货批次编号，即物流主单号（仅平台物流包含）
     */
    private String logisticsOrder;

    /**
     * 申请退货数量
     */
    private Integer requestQuantity;

    /**
     * 确认退货数量
     */
    private Integer confirmQuantity;

    /**
     * 实退数量
     */
    private Integer actualQuantity;

    /**
     * 平台SPU编号
     */
    private String platformSpuCode;

    /**
     * 商品类目ID
     */
    private String categoryId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getReasonType() {
        return reasonType;
    }

    public void setReasonType(String reasonType) {
        this.reasonType = reasonType;
    }

    public String getDetailedReason() {
        return detailedReason;
    }

    public void setDetailedReason(String detailedReason) {
        this.detailedReason = detailedReason;
    }

    public String getOutboundBatchCode() {
        return outboundBatchCode;
    }

    public void setOutboundBatchCode(String outboundBatchCode) {
        this.outboundBatchCode = outboundBatchCode;
    }

    public String getShippingProviderCode() {
        return shippingProviderCode;
    }

    public void setShippingProviderCode(String shippingProviderCode) {
        this.shippingProviderCode = shippingProviderCode;
    }

    public String getLogisticsOrder() {
        return logisticsOrder;
    }

    public void setLogisticsOrder(String logisticsOrder) {
        this.logisticsOrder = logisticsOrder;
    }

    public Integer getRequestQuantity() {
        return requestQuantity;
    }

    public void setRequestQuantity(Integer requestQuantity) {
        this.requestQuantity = requestQuantity;
    }

    public Integer getConfirmQuantity() {
        return confirmQuantity;
    }

    public void setConfirmQuantity(Integer confirmQuantity) {
        this.confirmQuantity = confirmQuantity;
    }

    public Integer getActualQuantity() {
        return actualQuantity;
    }

    public void setActualQuantity(Integer actualQuantity) {
        this.actualQuantity = actualQuantity;
    }

    public String getPlatformSpuCode() {
        return platformSpuCode;
    }

    public void setPlatformSpuCode(String platformSpuCode) {
        this.platformSpuCode = platformSpuCode;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }
}
