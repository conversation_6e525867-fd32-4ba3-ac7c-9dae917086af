package com.nsy.wms.repository.jpa.mapper.stockout;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.domain.stockout.StockOutOrderDetailExport;
import com.nsy.api.wms.domain.stockout.StockoutFbaReportExport;
import com.nsy.api.wms.domain.stockout.StockoutGenerateBatch;
import com.nsy.api.wms.domain.stockout.StockoutGenerateBatchId;
import com.nsy.api.wms.domain.stockout.StockoutGenerateBatchWhole;
import com.nsy.api.wms.domain.stockout.StockoutOrderList;
import com.nsy.api.wms.domain.stockout.StockoutOrderListCount;
import com.nsy.api.wms.domain.stockout.StockoutOrderPrematchInfo;
import com.nsy.api.wms.domain.stockout.StockoutOrderReturnProductList;
import com.nsy.api.wms.domain.stockout.StockoutStatusCount;
import com.nsy.api.wms.request.stockout.StockoutBatchOrderRequest;
import com.nsy.api.wms.request.stockout.StockoutGenerateBatchByDocRequest;
import com.nsy.api.wms.request.stockout.StockoutGenerateBatchDomesticWholeRequest;
import com.nsy.api.wms.request.stockout.StockoutGenerateBatchHotRequest;
import com.nsy.api.wms.request.stockout.StockoutGenerateBatchRequest;
import com.nsy.api.wms.request.stockout.StockoutGenerateBatchSecondRequest;
import com.nsy.api.wms.request.stockout.StockoutGenerateBatchWholeRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderListRequest;
import com.nsy.api.wms.response.stockout.StockoutBatchOrderResponse;
import com.nsy.api.wms.response.stockout.StockoutGenerateBatchByDocPrintResponse;
import com.nsy.api.wms.response.stockout.StockoutGenerateBatchByDocResponse;
import com.nsy.api.wms.response.stockout.StockoutGenerateBatchHotResponse;
import com.nsy.api.wms.response.stockout.StockoutOrderLackResponse;
import com.nsy.permission.annatation.Permission;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Component
public interface StockoutOrderMapper extends BaseMapper<StockoutOrderEntity> {
    @Permission
    IPage<StockoutOrderList> pageSearchList(IPage page, @Param("query") StockoutOrderListRequest request);

    IPage<StockOutOrderDetailExport> getStockoutOrderItemList(IPage page, @Param("query") StockoutOrderListRequest request);

    StockoutOrderListCount pageSearchCount(@Param("query") StockoutOrderListRequest request);

    List<StockoutStatusCount> statusCount(@Param("statusList") List<String> statusList);


    // 底下这些查询之所用InterceptorIgnore 是因为sql代码写法 mybatisplus解析不了，会报错，不能去掉
    @InterceptorIgnore(tenantLine = "true")
    IPage<StockoutGenerateBatch> findStockoutGenerateBatchList(IPage page, @Param("location") String location, @Param("request") StockoutGenerateBatchRequest request, @Param("workspace") String workspace, @Param("pickingType") String pickingType);

    @InterceptorIgnore(tenantLine = "true")
    List<StockoutGenerateBatchId> findStockoutIdsGenerateBatchList(@Param("location") String location, @Param("request") StockoutGenerateBatchRequest request, @Param("workspace") String workspace, @Param("pickingType") String pickingType);

    @InterceptorIgnore(tenantLine = "true")
    IPage<StockoutGenerateBatch> findSmallBagStockoutGenerateBatchList(IPage page, @Param("location") String location, @Param("request") StockoutGenerateBatchRequest request, @Param("workspace") String workspace, @Param("pickingType") String pickingType);

    @InterceptorIgnore(tenantLine = "true")
    List<StockoutGenerateBatchId> findSmallBagStockoutIdsGenerateBatchList(@Param("location") String location, @Param("request") StockoutGenerateBatchRequest request, @Param("workspace") String workspace, @Param("pickingType") String pickingType);


    @InterceptorIgnore(tenantLine = "true")
    IPage<StockoutGenerateBatchHotResponse> findStockoutGenerateBatchHotList(IPage page, @Param("location") String location, @Param("request") StockoutGenerateBatchHotRequest request);

    @InterceptorIgnore(tenantLine = "true")
    IPage<StockoutGenerateBatchByDocResponse> findFindGoodsByDoc(IPage page, @Param("location") String location, @Param("request") StockoutGenerateBatchByDocRequest request, @Param("workspace") String workspace);

    @InterceptorIgnore(tenantLine = "true")
    IPage<StockoutGenerateBatchWhole> findWholePick(IPage page, @Param("location") String location, @Param("request") StockoutGenerateBatchWholeRequest request, @Param("workspace") String workspace);

    @InterceptorIgnore(tenantLine = "true")
    List<StockoutGenerateBatch> findSecondSortList(@Param("location") String location, @Param("request") StockoutGenerateBatchSecondRequest request, @Param("workspace") String workspace);

    @InterceptorIgnore(tenantLine = "true")
    List<StockoutGenerateBatch> findWholePickList(@Param("location") String location, @Param("request") StockoutGenerateBatchDomesticWholeRequest request, @Param("workspace") String workspace);

    IPage<StockoutOrderReturnProductList> findReturnProductList(IPage page, @Param("scanNumber") String scanNumber);

    IPage<StockoutBatchOrderResponse> selectBatchDetailList(IPage page, @Param("batchIds") List<Integer> batchIds, @Param("request") StockoutBatchOrderRequest request);

    List<Integer> selectIds(@Param("logisticsCompanyList") List<String> logisticsCompanyList, @Param("logisticsNoList") List<String> logisticsNoList);

    IPage<StockoutGenerateBatchByDocPrintResponse> findFindGoodsByDocPrint(Page page, @Param("request") StockoutGenerateBatchByDocRequest request);

    List<String> selectByPickingTypeListAndStatusList(@Param("pickingTypeList") List<String> pickingTypeList, @Param("statusList") List<String> statusList,
                                                      @Param("brandList") List<String> brandList, @Param("brandName") String brandName);

    List<StockoutOrderLackResponse> selectLackList(@Param("stockoutOrderNos") List<String> stockoutOrderNos);

    @InterceptorIgnore(tenantLine = "true")
    StockoutOrderEntity findTopByOrderNo(@Param("orderNo") String orderNo);

    @InterceptorIgnore(tenantLine = "true")
    StockoutOrderEntity findTopByOrderNoOrShipmentId(@Param("orderNo") String orderNo, @Param("shipmentId") String shipmentId);

    @InterceptorIgnore(tenantLine = "true")
    StockoutOrderEntity findTopByOrderNoWithoutLocation(@Param("orderNo") String orderNo);

    // 查找pdd待生成波次
    List<StockoutGenerateBatch> findPddSecondSortList(@Param("location") String tenant, @Param("request") StockoutGenerateBatchSecondRequest request, @Param("workspace") String name);

    // 查找波次下平台
    List<String> findBatchOrderPlatform(@Param("batchId") Integer batchId);

    List<Integer> findReadyToGetTransparencyCode();

    @InterceptorIgnore(tenantLine = "true")
    List<StockoutOrderEntity> getDeliverDateIsNull(int from, int to);

    @InterceptorIgnore(tenantLine = "true")
    void updateDeliverDate(Integer stockoutOrderId, Date deliverDate);

    //判断出库单是否已经生成FBA补货单
    Boolean isCreateFbaReplenishOrder(String stockoutOrderNo);

    @InterceptorIgnore(tenantLine = "true")
    Integer countUnFullPrematchOrder(@Param("afterDate") Date afterDate);

    StockoutOrderPrematchInfo findPrematchInfoByStockOutId(Integer stockoutOrderId);

    Boolean isAllDeliverdByReplenishOrder(String replenishOrder);

    List<StockoutOrderEntity> listByShipmentIdList(List<Integer> shipmentIdList);

    //判断出库单下是否存在箱贴状态为申请中、申请完成、申请异常的装箱清单
    Boolean isApplyFbaLabel(String stockoutOrderNo);

    List<Integer> getStockoutIdByReplenishOrder(@Param("replenishOrder") String replenishOrder);

    //判断补货单号下的出库单状态都为待发货
    boolean isReplenishOrderWaitDelivery(String replenishOrder);

    List<StockoutFbaReportExport> exportFbaReport(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    Integer getStockoutOrderQty(@Param("stockoutOrderNoList") List<String> stockoutOrderNoList);

    /**
     * 查询超过七天未发货的订单详情
     *
     * @param statusList      订单状态列表
     * @param createStartDate 开始日期
     * @param createEndDate   结束日期
     * @return 订单详情列表
     */
    List<Map<String, Object>> findNoDeliveryOrderDetails(@Param("statusList") List<String> statusList,
                                                         @Param("createStartDate") Date createStartDate,
                                                         @Param("createEndDate") Date createEndDate);


    List<Integer> getStoreIdByPlatformNameAndCreateDate(String platformName, Date startDate);
}
