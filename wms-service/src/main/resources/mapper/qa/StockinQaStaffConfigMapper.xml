<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.qa.StockinQaStaffConfigMapper">

    <!-- 更新配置 -->
    <update id="updateConfig" parameterType="com.nsy.wms.repository.entity.qa.StockinQaStaffConfigEntity">
        UPDATE stockin_qa_staff_config
        <set>
            <if test="request.isWork != null">
                is_work = #{request.isWork},
            </if>
            <if test="request.majorCategoryId != null">
                major_category_id = #{request.majorCategoryId},
            </if>
            <if test="request.majorCategoryName != null and request.majorCategoryName != ''">
                major_category_name = #{request.majorCategoryName},
            </if>
            <if test="request.minorCategoryId != null">
                minor_category_id = #{request.minorCategoryId},
            </if>
            <if test="request.minorCategoryId == null || request.minorCategoryId == 0">
                minor_category_name = '',
            </if>
            <if test="request.minorCategoryName != null and request.minorCategoryName != ''">
                minor_category_name = #{request.minorCategoryName},
            </if>
            update_date = NOW(),
            update_by = #{request.updateBy}
        </set>
        WHERE id = #{request.id}
    </update>

    <!-- 分页查询质检人员配置 -->
    <select id="selectPageList" resultType="com.nsy.api.wms.response.qa.StockinQaStaffConfigPageResponse">
        SELECT 
            t.id,
            t.location,
            t.user_id,
            t.user_name,
            t.user_code,
            t.is_work,
            t.major_category_id,
            t.major_category_name,
            t.minor_category_id,
            t.minor_category_name,
            t.create_date,
            t.create_by,
            t.update_date,
            t.update_by,
            GROUP_CONCAT(DISTINCT s.space_name) as spaceName
        FROM stockin_qa_staff_config t
        INNER JOIN stockin_qa_staff_config_item ti on ti.config_id = t.id
        INNER JOIN bd_space s ON ti.space_id = s.space_id
        <where>
            <if test="request.userId != null">
                AND t.user_id = #{request.userId}
            </if>
            <if test="request.userName != null and request.userName != ''">
                AND t.user_name LIKE CONCAT('%', #{request.userName}, '%')
            </if>
            <if test="request.userCode != null and request.userCode != ''">
                AND t.user_code LIKE CONCAT('%', #{request.userCode}, '%')
            </if>
            <if test="request.isWork != null">
                AND t.is_work = #{request.isWork}
            </if>
            <if test="request.majorCategoryId != null">
                AND t.major_category_id = #{request.majorCategoryId}
            </if>
            <if test="request.minorCategoryId != null">
                AND t.minor_category_id = #{request.minorCategoryId}
            </if>
            <if test="request.spaceId != null">
                AND ti.space_id = #{request.spaceId}
            </if>
            <if test="request.groupUserIdList != null and request.groupUserIdList.size() > 0">
                AND t.user_id IN
                <foreach collection="request.groupUserIdList" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
        </where>
        group by t.id
        ORDER BY t.update_date DESC
    </select>
    <select id="listWorkStaff" resultType="com.nsy.api.wms.response.qa.StockinQaStaffConfigResponse">
        SELECT
            t.id,
            t.location,
            t.user_id,
            t.user_name,
            t.user_code,
            t.is_work,
            ti.space_id,
            t.major_category_id,
            t.major_category_name,
            t.minor_category_id,
            t.minor_category_name,
            t.create_date,
            t.create_by,
            t.update_date,
            t.update_by
        FROM stockin_qa_staff_config t
        INNER JOIN stockin_qa_staff_config_item ti on ti.config_id = t.id
        WHERE t.is_work = 1
    </select>

</mapper> 