<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.bd.BdSkcFifoRiskTypeMapper">

    <!-- 分页查询SKC FIFO配置信息 -->
    <select id="pageQuerySkcFifoInfoList" resultType="com.nsy.api.wms.request.bd.SkcFifoInfoDto">
        SELECT 
            f.skc as skc,
            CASE WHEN f.is_enable is null THEN NULL
                 WHEN f.is_enable = 1 THEN 1
                  ELSE 0 END as isFifo,
            COALESCE(r.riskTypeListStr, '') as riskTypeListStr,
            GREATEST(COALESCE(f.update_date, '1900-01-01'), COALESCE(r.maxRiskTypeUpdateDate, '1900-01-01')) as maxUpdateDate
        FROM bd_stock_fifo f
        LEFT JOIN (
            SELECT 
                skc,
                GROUP_CONCAT(DISTINCT risk_type ORDER BY risk_type SEPARATOR ',') as riskTypeListStr,
                MAX(update_date) as maxRiskTypeUpdateDate
            FROM bd_skc_fifo_risk_type
            GROUP BY skc
        ) r ON f.skc = r.skc
        <where>
            f.is_enable = 1
            <if test="query.skcList != null and query.skcList.size() > 0">
                AND f.skc in
                <foreach collection="query.skcList" separator="," index="index" item="skc" open="(" close=")">
                    #{skc}
                </foreach>
            </if>
            <if test="query.skc != null and query.skc != ''">
                AND f.skc like concat(#{query.skc}, '%')
            </if>
            <if test="query.riskTypeList != null and query.riskTypeList.size() > 0">
                AND EXISTS (
                    SELECT 1 FROM bd_skc_fifo_risk_type rt 
                    WHERE rt.skc = f.skc
                    AND rt.risk_type in
                        <foreach collection="query.riskTypeList" separator="," index="index" item="riskType" open="(" close=")">
                            #{riskType}
                        </foreach>
                )
            </if>
        </where>
        ORDER BY maxUpdateDate DESC, f.skc
    </select>

</mapper> 