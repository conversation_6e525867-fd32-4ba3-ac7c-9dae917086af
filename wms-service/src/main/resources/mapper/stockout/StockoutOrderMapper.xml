<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderMapper">
    <update id="updateDeliverDate">
        update stockout_order
        set delivery_date = #{deliverDate}
        where stockout_order_id = #{stockoutOrderId}
    </update>
    <select id="pageSearchList" resultType="com.nsy.api.wms.domain.stockout.StockoutOrderList">
        SELECT
        outOrder.stockout_order_id AS stockoutOrderId,
        outOrder.erp_pick_id AS erpPickId,
        outOrder.stockout_order_no AS stockoutOrderNo,
        space.space_name AS spaceName,
        desSpace.space_name AS desSpaceName,
        outOrder.stockout_type AS stockoutType,
        outOrder.picking_type AS pickingType,
        outOrder.workspace AS workspace,
        outOrder.business_type AS businessType,
        outOrder.`status` AS `status`,
        outOrder.is_need_process AS needProcess,
        outOrder.is_urgent AS urgent,
        outOrder.merge_state AS mergeState,
        outOrder.estimate_weight AS estimateWeight,
        outOrder.is_lack AS lack,
        outOrder.has_pack AS pack,
        outOrder.logistics_company AS logisticsCompany,
        outOrder.logistics_no AS logisticsNo,
        outOrder.store_name AS storeName,
        outOrder.notify_ship_status AS notifyShipStatus,
        outOrder.latest_delivery_date AS latestDeliveryDate,
        outOrder.ready_date AS readyDate,
        outOrder.saler AS saler,
        outOrder.des_space_id AS descSpaceId,
        outOrder.receiver_name as receiverName,
        outOrder.create_date AS createDate,
        outOrder.update_date AS updateDate,
        outOrder.update_by AS updateBy,
        outOrder.description as description,
        outOrder.platform_name,
        outOrder.replenish_order
        FROM
        stockout_order outOrder
        LEFT JOIN bd_space space ON space.space_id = outOrder.space_id
        LEFT JOIN bd_space desSpace ON desSpace.space_id = outOrder.des_space_id
        <if test="query!=null and query.expectedQty!=null">
            LEFT JOIN ( SELECT item.stockout_order_id AS outOrderId, sum(item.qty) as expectedQty
            FROM stockout_order_item item
            <where>
                <if test="query!=null and query.stockoutOrderIds!=null and query.stockoutOrderIds.get(0) != -1">
                    and item.stockout_order_id in
                    <foreach collection="query.stockoutOrderIds" separator="," index="index" item="stockoutOrderId"
                             open="("
                             close=")">
                        #{stockoutOrderId}
                    </foreach>
                </if>
                <if test="query!=null and query.createStartDate != null">
                    and item.create_date &gt;= #{query.createStartDate}
                </if>
                <if test="query!=null and query.createEndDate != null">
                    and item.create_date &lt;= #{query.createEndDate}
                </if>
            </where>
            GROUP BY item.stockout_order_id
            <trim prefix="having" suffixOverrides="and">
                <if test="query!=null and query.expectedQty != null">
                    expectedQty &gt; #{query.expectedQty} and
                </if>
            </trim>
            ) item ON item.outOrderId = outOrder.stockout_order_id
        </if>
        <if test="query.countryCode!=null or query.receiverName!=null">
            LEFT JOIN stockout_receiver_info sri on outOrder.stockout_order_id = sri.stockout_order_id
        </if>
        <if test="query.isAddVas!=null or (query.spaceAreaIds !=null  and query.spaceAreaIds.size() > 0) or query.isTransparency!=null">
            INNER JOIN ( SELECT DISTINCT item.stockout_order_id AS outOrderId
            FROM stockout_order_item item
            <if test="query.spaceAreaIds !=null  and query.spaceAreaIds.size() > 0">
                LEFT JOIN bd_space_area bsa on bsa.space_area_name = item.space_area_name
            </if>
            <where>
                <if test="query!=null and query.createStartDate != null">
                    and item.create_date &gt;= #{query.createStartDate}
                </if>
                <if test="query!=null and query.createEndDate != null">
                    and item.create_date &lt;= #{query.createEndDate}
                </if>
                <if test="query!=null and query.isTransparency!=null ">
                    and item.is_transparency = #{query.isTransparency}
                </if>
                <if test="query!=null and query.isAddVas!=null and query.isAddVas == 1 ">
                    and item.vas_type &lt;&gt; ''
                </if>
                <if test="query!=null and query.isAddVas!=null and query.isAddVas == 0 ">
                    and item.vas_type = ''
                </if>
                <if test="query.spaceAreaIds !=null  and query.spaceAreaIds.size() > 0">
                    and bsa.space_area_id in
                    <foreach collection="query.spaceAreaIds" separator="," index="index" item="spaceAreaId"
                             open="("
                             close=")">
                        #{spaceAreaId}
                    </foreach>
                </if>
            </where>
            ) vas ON vas.outOrderId = outOrder.stockout_order_id
        </if>
        <if test="query.tagIdList!=null and query.tagIdList.size() > 0">
            INNER JOIN (
            select
            *
            from bd_tag_mapping
            where reference_type = 'ORDER'
            and tag_id in
            <foreach collection="query.tagIdList" separator="," index="index" item="tagId" open="("
                     close=")">
                #{tagId}
            </foreach>
            ) btm on btm.reference_no = outOrder.stockout_order_no
        </if>
        <where>
            <if test="query!=null and query.expectedQty!=null">
                and item.outOrderId is not null
            </if>
            <if test="query!=null and query.stockoutOrderIds!=null and query.stockoutOrderIds.get(0) != -1">
                and outOrder.stockout_order_id in
                <foreach collection="query.stockoutOrderIds" separator="," index="index" item="stockoutOrderId" open="("
                         close=")">
                    #{stockoutOrderId}
                </foreach>
            </if>
            <if test="query!=null and query.stockoutOrderNo != null and query.stockoutOrderNo !=''">
                and outOrder.stockout_order_no = #{query.stockoutOrderNo}
            </if>
            <if test="query.replenishOrder != null and query.replenishOrder !=''">
                and outOrder.replenish_order = #{query.replenishOrder}
            </if>
            <if test="query!=null and query.stockoutOrderNo != null and query.stockoutOrderNo !=''">
                and outOrder.stockout_order_no = #{query.stockoutOrderNo}
            </if>
            <if test="query!=null and query.pickingTypes != null and query.pickingTypes.size() > 0">
                and outOrder.picking_type in
                <foreach collection="query.pickingTypes" separator="," index="index" item="pickingType" open="("
                         close=")">
                    #{pickingType}
                </foreach>
            </if>
            <if test="query!=null and query.spaceIds != null and query.spaceIds.size() > 0">
                and outOrder.space_id in
                <foreach collection="query.spaceIds" separator="," index="index" item="spaceId" open="("
                         close=")">
                    #{spaceId}
                </foreach>
            </if>
            <if test="query!=null and query.storeIdList != null and query.storeIdList.size() > 0">
                and outOrder.store_id in
                <foreach collection="query.storeIdList" separator="," index="index" item="storeId" open="("
                         close=")">
                    #{storeId}
                </foreach>
            </if>
            <if test="query!=null and query.logisticsCompanys != null and query.logisticsCompanys.size() > 0">
                and outOrder.logistics_company in
                <foreach collection="query.logisticsCompanys" separator="," index="index" item="logisticsCompany"
                         open="("
                         close=")">
                    #{logisticsCompany}
                </foreach>
            </if>
            <if test="query!=null and query.statusList != null and query.statusList.size() > 0">
                and outOrder.`status` in
                <foreach collection="query.statusList" separator="," index="index" item="status"
                         open="("
                         close=")">
                    #{status}
                </foreach>
            </if>
            <if test="query!=null and query.workspace != null and query.workspace.size() > 0">
                and outOrder.workspace in
                <foreach collection="query.workspace" separator="," index="index" item="workspace" open="("
                         close=")">
                    #{workspace}
                </foreach>
            </if>
            <if test="query!=null and query.urgent != null">
                and outOrder.is_urgent = #{query.urgent}
            </if>
            <if test="query!=null and query.pack != null">
                and outOrder.has_pack = #{query.pack}
            </if>
            <if test="query!=null and query.merge != null and query.merge !=''">
                and outOrder.merge_state != null
            </if>
            <if test="query!=null and query.isProcess != null">
                and outOrder.is_need_process = #{query.isProcess}
            </if>
            <if test="query!=null and query.lack != null">
                and outOrder.is_lack = #{query.lack}
            </if>
            <if test="query.logisticsNo != null and query.logisticsNo !=''">
                and outOrder.logistics_no = #{query.logisticsNo}
            </if>
            <if test="query.erpPickId != null">
                and outOrder.erp_pick_id = #{query.erpPickId}
            </if>
            <if test="query!=null and query.storeName != null and query.storeName !=''">
                and outOrder.store_name like concat('%',#{query.storeName},'%')
            </if>
            <if test="query!=null and query.notifyShipStatus != null and query.notifyShipStatus !=''">
                and outOrder.notify_ship_status = #{query.notifyShipStatus}
            </if>
            <if test="query!=null and query.stockoutOrderNoList != null and query.stockoutOrderNoList.size() > 0">
                and outOrder.stockout_order_no in
                <foreach collection="query.stockoutOrderNoList" separator="," index="index" item="stockoutOrderNoDetail"
                         open="("
                         close=")">
                    #{stockoutOrderNoDetail}
                </foreach>
            </if>
            <if test="query!=null and query.logisticsNoList != null and query.logisticsNoList.size() > 0">
                and outOrder.logistics_no in
                <foreach collection="query.logisticsNoList" separator="," index="index" item="logisticsNoDetail"
                         open="("
                         close=")">
                    #{logisticsNoDetail}
                </foreach>
            </if>
            <if test="query!=null and query.businessTypes != null and query.businessTypes.size() > 0">
                and outOrder.business_type in
                <foreach collection="query.businessTypes" separator="," index="index" item="businessType" open="("
                         close=")">
                    #{businessType}
                </foreach>
            </if>
            <if test="query.stockoutTypes != null and query.stockoutTypes.size() > 0">
                and outOrder.stockout_type in
                <foreach collection="query.stockoutTypes" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query!=null and query.status != null and query.status !=''">
                and outOrder.`status` = #{query.status}
            </if>
            <if test="query!=null and query.areaNameList != null and query.areaNameList.size() > 0">
                and outOrder.area_name in
                <foreach collection="query.areaNameList" separator="," index="index" item="areaName" open="("
                         close=")">
                    #{areaName}
                </foreach>
            </if>
            <if test="query!=null and query.estimateWeightLow != null and query.estimateWeightLow !=''">
                and outOrder.estimate_weight &gt;= #{query.estimateWeightLow}
            </if>
            <if test="query!=null and query.estimateWeightHigh != null and query.estimateWeightHigh !=''">
                and outOrder.estimate_weight &lt;= #{query.estimateWeightHigh}
            </if>
            <if test="query!=null and query.exceptStartDate != null">
                and outOrder.delivery_date &gt;= #{query.exceptStartDate}
            </if>
            <if test="query!=null and query.exceptEndDate != null">
                and outOrder.delivery_date &lt;= #{query.exceptEndDate}
            </if>

            <if test="query!=null and query.createStartDate != null">
                and outOrder.create_date &gt;= #{query.createStartDate}
            </if>
            <if test="query!=null and query.createEndDate != null">
                and outOrder.create_date &lt;= #{query.createEndDate}
            </if>
            <if test="query.countryCode!=null and query.countryCode!=''">
                and sri.country_code = #{query.countryCode}
            </if>
            <if test="query.receiverName!=null and query.receiverName!=''">
                and sri.receiver_name = #{query.receiverName}
            </if>
            <if test="query.platformName!=null and query.platformName!=''">
                and outOrder.platform_name = #{query.platformName}
            </if>
        </where>
        <if test="query!=null and query.statusList != null and query.statusList.size() == 1 and query.statusList[0] == 'READY_DELIVERY'">
            order by outOrder.update_date desc
        </if>
        <if test="query==null or query.statusList == null or query.statusList.size() == 0 or query.statusList.size() > 1 or query.statusList[0] != 'READY_DELIVERY'">
            order by outOrder.create_date desc
        </if>
    </select>
    <select id="getStockoutOrderItemList" resultType="com.nsy.api.wms.domain.stockout.StockOutOrderDetailExport">
        SELECT
        outOrder.stockout_order_id AS stockoutOrderId,
        outOrder.stockout_order_no AS stockoutOrderNo,
        i.order_no AS orderNo,
        space.space_name AS spaceName,
        outOrder.stockout_type AS stockoutType,
        outOrder.picking_type AS pickingType,
        outOrder.workspace AS workspace,
        outOrder.business_type AS businessType,
        outOrder.`status` AS `status`,
        i.sku AS sku,
        i.qty AS qty,
        i.lack_qty as lackQty,
        i.shipment_qty as shipmentQty,
        i.is_need_process AS isProcess,
        outOrder.is_urgent AS isUrgent,
        outOrder.merge_state AS mergeState,
        outOrder.estimate_weight AS estimateWeight,
        outOrder.is_lack AS isLack,
        outOrder.logistics_company AS logisticsCompany,
        outOrder.logistics_no AS logisticsNo,
        outOrder.store_name AS storeName,
        outOrder.latest_delivery_date AS expectShipDate,
        outOrder.notify_ship_status AS notifyShipStatus,
        outOrder.saler AS saler,
        outOrder.create_date AS createDate,
        outOrder.delivery_date as deliveryDate,
        i.is_transparency AS isTransparency,
        i.seller_title,
        i.seller_sku,
        i.seller_barcode,
        i.position_code
        FROM
        stockout_order_item i
        INNER JOIN stockout_order outOrder on   i.stockout_order_id = outOrder.stockout_order_id
        LEFT JOIN bd_space space ON space.space_id = outOrder.space_id
        <if test="query!=null and query.expectedQty!=null">
            LEFT JOIN ( SELECT item.stockout_order_id AS outOrderId, sum(item.qty) as expectedQty
            FROM stockout_order_item item
            <where>
                <if test="query!=null and query.stockoutOrderIds!=null and query.stockoutOrderIds.get(0) != -1">
                    and item.stockout_order_id in
                    <foreach collection="query.stockoutOrderIds" separator="," index="index" item="stockoutOrderId"
                             open="("
                             close=")">
                        #{stockoutOrderId}
                    </foreach>
                </if>
                <if test="query!=null and query.createStartDate != null">
                    and item.create_date &gt;= #{query.createStartDate}
                </if>
                <if test="query!=null and query.createEndDate != null">
                    and item.create_date &lt;= #{query.createEndDate}
                </if>
            </where>
            GROUP BY item.stockout_order_id
            <trim prefix="having" suffixOverrides="and">
                <if test="query!=null and query.expectedQty != null">
                    expectedQty &gt; #{query.expectedQty} and
                </if>
            </trim>
            ) item ON item.outOrderId = outOrder.stockout_order_id
        </if>
        <if test="query.countryCode!=null or query.receiverName!=null">
            LEFT JOIN stockout_receiver_info sri on outOrder.stockout_order_id = sri.stockout_order_id
        </if>
        <if test="query.isAddVas!=null or (query.spaceAreaIds !=null  and query.spaceAreaIds.size() > 0)">
            INNER JOIN ( SELECT DISTINCT item.stockout_order_id AS outOrderId
            FROM stockout_order_item item
            <if test="query.spaceAreaIds !=null  and query.spaceAreaIds.size() > 0">
                LEFT JOIN bd_space_area bsa on bsa.space_area_name = item.space_area_name
            </if>
            <where>
                <if test="query!=null and query.createStartDate != null">
                    and item.create_date &gt;= #{query.createStartDate}
                </if>
                <if test="query!=null and query.createEndDate != null">
                    and item.create_date &lt;= #{query.createEndDate}
                </if>
                <if test="query!=null and query.isAddVas!=null and query.isAddVas == 1 ">
                    and item.vas_type &lt;&gt; ''
                </if>
                <if test="query!=null and query.isAddVas!=null and query.isAddVas == 0 ">
                    and item.vas_type = ''
                </if>
                <if test="query.spaceAreaIds !=null  and query.spaceAreaIds.size() > 0">
                    and bsa.space_area_id in
                    <foreach collection="query.spaceAreaIds" separator="," index="index" item="spaceAreaId"
                             open="("
                             close=")">
                        #{spaceAreaId}
                    </foreach>
                </if>
            </where>
            ) vas ON vas.outOrderId = outOrder.stockout_order_id
        </if>
        <where>
            <if test="query!=null and query.expectedQty!=null">
                and item.outOrderId is not null
            </if>
            <if test="query!=null and query.stockoutOrderIds!=null and query.stockoutOrderIds.get(0) != -1">
                and outOrder.stockout_order_id in
                <foreach collection="query.stockoutOrderIds" separator="," index="index" item="stockoutOrderId" open="("
                         close=")">
                    #{stockoutOrderId}
                </foreach>
            </if>
            <if test="query!=null and query.stockoutOrderNo != null and query.stockoutOrderNo !=''">
                and outOrder.stockout_order_no = #{query.stockoutOrderNo}
            </if>
            <if test="query!=null and query.pickingTypes != null and query.pickingTypes.size() > 0">
                and outOrder.picking_type in
                <foreach collection="query.pickingTypes" separator="," index="index" item="pickingType" open="("
                         close=")">
                    #{pickingType}
                </foreach>
            </if>
            <if test="query!=null and query.spaceIds != null and query.spaceIds.size() > 0">
                and outOrder.space_id in
                <foreach collection="query.spaceIds" separator="," index="index" item="spaceId" open="("
                         close=")">
                    #{spaceId}
                </foreach>
            </if>
            <if test="query!=null and query.storeIdList != null and query.storeIdList.size() > 0">
                and outOrder.store_id in
                <foreach collection="query.storeIdList" separator="," index="index" item="storeId" open="("
                         close=")">
                    #{storeId}
                </foreach>
            </if>
            <if test="query!=null and query.logisticsCompanys != null and query.logisticsCompanys.size() > 0">
                and outOrder.logistics_company in
                <foreach collection="query.logisticsCompanys" separator="," index="index" item="logisticsCompany"
                         open="("
                         close=")">
                    #{logisticsCompany}
                </foreach>
            </if>
            <if test="query!=null and query.statusList != null and query.statusList.size() > 0">
                and outOrder.`status` in
                <foreach collection="query.statusList" separator="," index="index" item="status"
                         open="("
                         close=")">
                    #{status}
                </foreach>
            </if>
            <if test="query!=null and query.workspace != null and query.workspace.size() > 0">
                and outOrder.workspace in
                <foreach collection="query.workspace" separator="," index="index" item="workspace" open="("
                         close=")">
                    #{workspace}
                </foreach>
            </if>
            <if test="query!=null and query.urgent != null">
                and outOrder.is_urgent = #{query.urgent}
            </if>
            <if test="query!=null and query.pack != null">
                and outOrder.has_pack = #{query.pack}
            </if>
            <if test="query!=null and query.merge != null and query.merge !=''">
                and outOrder.merge_state != null
            </if>
            <if test="query!=null and query.isProcess != null">
                and outOrder.is_need_process = #{query.isProcess}
            </if>
            <if test="query!=null and query.lack != null">
                and outOrder.is_lack = #{query.lack}
            </if>
            <if test="query.logisticsNo != null and query.logisticsNo !=''">
                and outOrder.logistics_no = #{query.logisticsNo}
            </if>
            <if test="query!=null and query.storeName != null and query.storeName !=''">
                and outOrder.store_name like concat('%',#{query.storeName},'%')
            </if>
            <if test="query!=null and query.notifyShipStatus != null and query.notifyShipStatus !=''">
                and outOrder.notify_ship_status = #{query.notifyShipStatus}
            </if>
            <if test="query!=null and query.areaNameList != null and query.areaNameList.size() > 0">
                and outOrder.area_name in
                <foreach collection="query.areaNameList" separator="," index="index" item="areaName" open="("
                         close=")">
                    #{areaName}
                </foreach>
            </if>
            <if test="query!=null and query.stockoutOrderNoList != null and query.stockoutOrderNoList.size() > 0">
                and outOrder.stockout_order_no in
                <foreach collection="query.stockoutOrderNoList" separator="," index="index" item="stockoutOrderNoDetail"
                         open="("
                         close=")">
                    #{stockoutOrderNoDetail}
                </foreach>
            </if>
            <if test="query!=null and query.logisticsNoList != null and query.logisticsNoList.size() > 0">
                and outOrder.logistics_no in
                <foreach collection="query.logisticsNoList" separator="," index="index" item="logisticsNoDetail"
                         open="("
                         close=")">
                    #{logisticsNoDetail}
                </foreach>
            </if>
            <if test="query!=null and query.businessTypes != null and query.businessTypes.size() > 0">
                and outOrder.business_type in
                <foreach collection="query.businessTypes" separator="," index="index" item="businessType" open="("
                         close=")">
                    #{businessType}
                </foreach>
            </if>
            <if test="query.stockoutTypes != null and query.stockoutTypes.size() > 0">
                and outOrder.stockout_type in
                <foreach collection="query.stockoutTypes" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query!=null and query.status != null and query.status !=''">
                and outOrder.`status` = #{query.status}
            </if>
            <if test="query!=null and query.estimateWeightLow != null and query.estimateWeightLow !=''">
                and outOrder.estimate_weight &gt;= #{query.estimateWeightLow}
            </if>
            <if test="query!=null and query.estimateWeightHigh != null and query.estimateWeightHigh !=''">
                and outOrder.estimate_weight &lt;= #{query.estimateWeightHigh}
            </if>
            <if test="query!=null and query.exceptStartDate != null">
                and outOrder.delivery_date &gt;= #{query.exceptStartDate}
            </if>
            <if test="query!=null and query.exceptEndDate != null">
                and outOrder.delivery_date &lt;= #{query.exceptEndDate}
            </if>

            <if test="query!=null and query.createStartDate != null">
                and outOrder.create_date &gt;= #{query.createStartDate}
            </if>
            <if test="query!=null and query.createEndDate != null">
                and outOrder.create_date &lt;= #{query.createEndDate}
            </if>
            <if test="query.countryCode!=null and query.countryCode!=''">
                and sri.country_code = #{query.countryCode}
            </if>
            <if test="query.receiverName!=null and query.receiverName!=''">
                and sri.receiver_name = #{query.receiverName}
            </if>
            <if test="query.platformName!=null and query.platformName!=''">
                and outOrder.platform_name = #{query.platformName}
            </if>
        </where>
        <if test="query!=null and query.statusList != null and query.statusList.size() == 1 and query.statusList[0] == 'READY_DELIVERY'">
            order by outOrder.update_date desc
        </if>
        <if test="query==null or query.statusList == null or query.statusList.size() == 0 or query.statusList.size() > 1 or query.statusList[0] != 'READY_DELIVERY'">
            order by outOrder.create_date desc
        </if>
    </select>

    <select id="pageSearchCount" resultType="com.nsy.api.wms.domain.stockout.StockoutOrderListCount">
        select
        ifnull(sum(t.waitPickedQty), 0) as waitPickedQty,
        ifnull(sum(t.scanQty), 0) as scanQty,
        ifnull(sum(t.shipmentQty), 0) as shipmentQty,
        ifnull(sum(t.estimate_weight), 0) as estimate_weight
        from(
        SELECT
        outOrder.stockout_order_id,
        ifnull(sum(soi.qty), 0) as waitPickedQty,
        ifnull(sum(soi.scan_qty), 0) as scanQty,
        ifnull(sum(soi.shipment_qty), 0) as shipmentQty,
        outOrder.estimate_weight
        FROM
        stockout_order outOrder
        LEFT JOIN bd_space space ON space.space_id = outOrder.space_id
        left join stockout_order_item soi on outOrder.stockout_order_id = soi.stockout_order_id
        <if test="query.countryCode!=null or query.receiverName!=null">
            LEFT JOIN stockout_receiver_info sri on outOrder.stockout_order_id = sri.stockout_order_id
        </if>
        <if test="query.isAddVas!=null or (query.spaceAreaIds !=null  and query.spaceAreaIds.size() > 0)">
            INNER JOIN ( SELECT DISTINCT item.stockout_order_id AS outOrderId
            FROM stockout_order_item item
            <if test="query.spaceAreaIds !=null  and query.spaceAreaIds.size() > 0">
                LEFT JOIN bd_space_area bsa on bsa.space_area_name = item.space_area_name
            </if>
            <where>
                <if test="query!=null and query.createStartDate != null">
                    and item.create_date &gt;= #{query.createStartDate}
                </if>
                <if test="query!=null and query.createEndDate != null">
                    and item.create_date &lt;= #{query.createEndDate}
                </if>
                <if test="query!=null and query.isAddVas!=null and query.isAddVas == 1 ">
                    and item.vas_type &lt;&gt; ''
                </if>
                <if test="query!=null and query.isAddVas!=null and query.isAddVas == 0 ">
                    and item.vas_type = ''
                </if>
                <if test="query.spaceAreaIds !=null  and query.spaceAreaIds.size() > 0">
                    and bsa.space_area_id in
                    <foreach collection="query.spaceAreaIds" separator="," index="index" item="spaceAreaId"
                             open="("
                             close=")">
                        #{spaceAreaId}
                    </foreach>
                </if>
            </where>
            ) vas ON vas.outOrderId = outOrder.stockout_order_id
        </if>
        <where>
            <if test="query!=null and query.stockoutOrderIds!=null and query.stockoutOrderIds.get(0) != -1">
                and outOrder.stockout_order_id in
                <foreach collection="query.stockoutOrderIds" separator="," index="index" item="stockoutOrderId" open="("
                         close=")">
                    #{stockoutOrderId}
                </foreach>
            </if>
            <if test="query!=null and query.stockoutOrderNo != null and query.stockoutOrderNo !=''">
                and outOrder.stockout_order_no = #{query.stockoutOrderNo}
            </if>
            <if test="query!=null and query.pickingTypes != null and query.pickingTypes.size() > 0">
                and outOrder.picking_type in
                <foreach collection="query.pickingTypes" separator="," index="index" item="pickingType" open="("
                         close=")">
                    #{pickingType}
                </foreach>
            </if>
            <if test="query!=null and query.spaceIds != null and query.spaceIds.size() > 0">
                and outOrder.space_id in
                <foreach collection="query.spaceIds" separator="," index="index" item="spaceId" open="("
                         close=")">
                    #{spaceId}
                </foreach>
            </if>
            <if test="query!=null and query.storeIdList != null and query.storeIdList.size() > 0">
                and outOrder.store_id in
                <foreach collection="query.storeIdList" separator="," index="index" item="storeId" open="("
                         close=")">
                    #{storeId}
                </foreach>
            </if>
            <if test="query!=null and query.logisticsCompanys != null and query.logisticsCompanys.size() > 0">
                and outOrder.logistics_company in
                <foreach collection="query.logisticsCompanys" separator="," index="index" item="logisticsCompany"
                         open="("
                         close=")">
                    #{logisticsCompany}
                </foreach>
            </if>
            <if test="query!=null and query.statusList != null and query.statusList.size() > 0">
                and outOrder.`status` in
                <foreach collection="query.statusList" separator="," index="index" item="status"
                         open="("
                         close=")">
                    #{status}
                </foreach>
            </if>
            <if test="query!=null and query.statusList.size()==0">
                and outOrder.`status` != 'CANCELLING' AND outOrder.`status` != 'CANCELLED' AND outOrder.`status` !=
                'COMPLETE'
            </if>
            <if test="query!=null and query.workspace != null and query.workspace.size() > 0">
                and outOrder.workspace in
                <foreach collection="query.workspace" separator="," index="index" item="workspace" open="("
                         close=")">
                    #{workspace}
                </foreach>
            </if>
            <if test="query!=null and query.urgent != null">
                and outOrder.is_urgent = #{query.urgent}
            </if>
            <if test="query!=null and query.merge != null and query.merge !=''">
                and outOrder.merge_state != null
            </if>
            <if test="query!=null and query.isProcess != null">
                and outOrder.is_need_process = #{query.isProcess}
            </if>
            <if test="query!=null and query.lack != null">
                and outOrder.is_lack = #{query.lack}
            </if>
            <if test="query.logisticsNo != null and query.logisticsNo !=''">
                and outOrder.logistics_no = #{query.logisticsNo}
            </if>
            <if test="query!=null and query.storeName != null and query.storeName !=''">
                and outOrder.store_name like concat('%',#{query.storeName},'%')
            </if>
            <if test="query!=null and query.notifyShipStatus != null and query.notifyShipStatus !=''">
                and outOrder.notify_ship_status = #{query.notifyShipStatus}
            </if>
            <if test="query!=null and query.areaNameList != null and query.areaNameList.size() > 0">
                and outOrder.area_name in
                <foreach collection="query.areaNameList" separator="," index="index" item="areaName" open="("
                         close=")">
                    #{areaName}
                </foreach>
            </if>
            <if test="query!=null and query.businessTypes != null and query.businessTypes.size() > 0">
                and outOrder.business_type in
                <foreach collection="query.businessTypes" separator="," index="index" item="businessType" open="("
                         close=")">
                    #{businessType}
                </foreach>
            </if>
            <if test="query.stockoutTypes != null and query.stockoutTypes.size() > 0">
                and outOrder.stockout_type in
                <foreach collection="query.stockoutTypes" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query!=null and query.status != null and query.status !=''">
                and outOrder.`status` = #{query.status}
            </if>
            <if test="query!=null and query.estimateWeightLow != null and query.estimateWeightLow !=''">
                and outOrder.estimate_weight &gt;= #{query.estimateWeightLow}
            </if>
            <if test="query!=null and query.estimateWeightHigh != null and query.estimateWeightHigh !=''">
                and outOrder.estimate_weight &lt;= #{query.estimateWeightHigh}
            </if>
            <if test="query!=null and query.exceptStartDate != null">
                and outOrder.delivery_date &gt;= #{query.exceptStartDate}
            </if>
            <if test="query!=null and query.exceptEndDate != null">
                and outOrder.delivery_date &lt;= #{query.exceptEndDate}
            </if>

            <if test="query!=null and query.createStartDate != null">
                and outOrder.create_date &gt;= #{query.createStartDate}
            </if>
            <if test="query!=null and query.createEndDate != null">
                and outOrder.create_date &lt;= #{query.createEndDate}
            </if>
            <if test="query.countryCode!=null and query.countryCode!=''">
                and sri.country_code = #{query.countryCode}
            </if>
            <if test="query.receiverName!=null  and query.receiverName!=''">
                and sri.receiver_name = #{query.receiverName}
            </if>
            <if test="query.platformName!=null and query.platformName!=''">
                and outOrder.platform_name = #{query.platformName}
            </if>
            <if test="query!=null and query.stockoutOrderNoList != null and query.stockoutOrderNoList.size() > 0">
                and outOrder.stockout_order_no in
                <foreach collection="query.stockoutOrderNoList" separator="," index="index" item="stockoutOrderNoDetail"
                         open="("
                         close=")">
                    #{stockoutOrderNoDetail}
                </foreach>
            </if>
            <if test="query!=null and query.logisticsNoList != null and query.logisticsNoList.size() > 0">
                and outOrder.logistics_no in
                <foreach collection="query.logisticsNoList" separator="," index="index" item="logisticsNoDetail"
                         open="("
                         close=")">
                    #{logisticsNoDetail}
                </foreach>
            </if>
        </where>
        GROUP BY stockout_order_id
        <trim prefix="having" suffixOverrides="and">
            <if test="query!=null and query.expectedQty != null">
                waitPickedQty &gt; #{query.expectedQty} and
            </if>
        </trim>
        ) t

    </select>

    <select id="statusCount" resultType="com.nsy.api.wms.domain.stockout.StockoutStatusCount">
        SELECT
        count( outOrder.`status` ) AS countNum,
        outOrder.`status` AS `status`
        FROM
        stockout_order outOrder
        <where>
            <if test="statusList!=null and statusList.size()>0">
                and outOrder.status in
                <foreach collection="statusList" separator="," index="index" item="status" open="("
                         close=")">
                    #{status}
                </foreach>
            </if>
        </where>
        GROUP BY
        outOrder.`status`
    </select>

    <select id="findStockoutGenerateBatchList" resultType="com.nsy.api.wms.domain.stockout.StockoutGenerateBatch">
        select t.logistics_company as logisticsCompany,t.logistics_label_size as logisticsLabelSize,
        m.tag_name as tagName,
        sum(t.status = 'READY_WAVE_GENERATED') as readyGeneratedBatchQty,
        sum(t.status in ('READY_PICK','PICKING','READY_OUTBOUND','OUTBOUNDING','READY_DELIVERY')) as inTransitBatchQty,
        sum((select sum(s.qty) from stockout_order_item s where t.stockout_order_id =
        s.stockout_order_id)) as skuQty
        FROM stockout_order t
        left join bd_tag_mapping m on m.reference_no = t.stockout_order_no
        and m.reference_type = 'ORDER'
        and m.tag_name in
        <foreach collection="request.brandList" separator="," index="index" item="brand" open="("
                 close=")">
            #{brand}
        </foreach>
        WHERE t.location = #{location}
        and t.status in ('READY_WAVE_GENERATED','READY_PICK','PICKING','READY_OUTBOUND','OUTBOUNDING','READY_DELIVERY')
        and t.workspace = #{workspace}
        and t.picking_type = #{pickingType}
        and t.stockout_type != 'OVERSEA_DELIVERY'
        <if test="workspace == 'B2C_BAG_AREA'">
            and t.store_id != 8886
        </if>
        <if test="request!=null and request.stockoutType != null and request.stockoutType !=''">
            and t.stockout_type = #{request.stockoutType}
        </if>
        <if test="request!=null and request.spaceId != null">
            and t.space_id = #{request.spaceId}
        </if>
        <if test="request!=null and request.logisticsCompany != null and request.logisticsCompany !=''">
            and t.logistics_company =#{request.logisticsCompany}
        </if>
        <if test="request!=null and request.logisticsLabelSize != null and request.logisticsLabelSize !=''">
            and t.logistics_label_size =#{request.logisticsLabelSize}
        </if>
        <if test="request!=null and request.businessType != null and request.businessType !=''">
            and t.business_type =#{request.businessType}
        </if>
        <if test="request!=null and request.isUrgent != null">
            and t.is_urgent = #{request.isUrgent}
        </if>
        <if test="request!=null and request.isNeedProcess != null">
            and t.is_need_process = #{request.isNeedProcess}
        </if>
        <if test="request!=null and request.multipleSpace != null">
            and t.multiple_space = #{request.multipleSpace}
        </if>
        <if test="request.tagName != null and request.tagName !=''">
            and m.tag_name = #{request.tagName}
        </if>

        GROUP BY t.logistics_company,t.logistics_label_size,m.tag_name
        ORDER BY t.logistics_company desc
    </select>

    <select id="findStockoutIdsGenerateBatchList" resultType="com.nsy.api.wms.domain.stockout.StockoutGenerateBatchId">
        select t.logistics_company as logisticsCompany,
        t.logistics_label_size as logisticsLabelSize,
        t.stockout_order_id as stockoutOrderId,
        m.tag_name as tagName
        FROM stockout_order t
        left join bd_tag_mapping m on m.reference_no = t.stockout_order_no
        and m.reference_type = 'ORDER'
        and m.tag_name in
        <foreach collection="request.brandList" separator="," index="index" item="brand" open="("
                 close=")">
            #{brand}
        </foreach>
        WHERE t.location = #{location}
        and t.status = 'READY_WAVE_GENERATED'
        and t.workspace = #{workspace}
        and t.picking_type = #{pickingType}
        and t.stockout_type != 'OVERSEA_DELIVERY'
        <if test="workspace == 'B2C_BAG_AREA'">
            and t.store_id != 8886
        </if>
        <if test="request!=null and request.stockoutType != null and request.stockoutType !=''">
            and t.stockout_type = #{request.stockoutType}
        </if>
        <if test="request!=null and request.spaceId != null">
            and t.space_id = #{request.spaceId}
        </if>
        <if test="request!=null and request.logisticsCompany != null and request.logisticsCompany !=''">
            and t.logistics_company =#{request.logisticsCompany}
        </if>
        <if test="request!=null and request.logisticsLabelSize != null and request.logisticsLabelSize !=''">
            and t.logistics_label_size =#{request.logisticsLabelSize}
        </if>
        <if test="request!=null and request.businessType != null and request.businessType !=''">
            and t.business_type =#{request.businessType}
        </if>
        <if test="request!=null and request.isUrgent != null">
            and t.is_urgent = #{request.isUrgent}
        </if>
        <if test="request!=null and request.isNeedProcess != null">
            and t.is_need_process = #{request.isNeedProcess}
        </if>
        <if test="request!=null and request.multipleSpace != null">
            and t.multiple_space = #{request.multipleSpace}
        </if>
        <if test="request.tagName != null and request.tagName !=''">
            and m.tag_name = #{request.tagName}
        </if>
    </select>


    <select id="findSmallBagStockoutGenerateBatchList"
            resultType="com.nsy.api.wms.domain.stockout.StockoutGenerateBatch">
        select t.logistics_company as logisticsCompany,m.tag_name as tagName,
        sum(t.status = 'READY_WAVE_GENERATED') as readyGeneratedBatchQty,
        sum(t.status in ('READY_PICK','PICKING','READY_OUTBOUND','OUTBOUNDING','READY_DELIVERY')) as inTransitBatchQty,
        sum((select sum(s.qty) from stockout_order_item s where t.stockout_order_id =
        s.stockout_order_id)) as skuQty
        FROM stockout_order t
        left join bd_tag_mapping m on m.reference_no = t.stockout_order_no
        and m.reference_type = 'ORDER'
        and m.tag_name in
        <foreach collection="request.brandList" separator="," index="index" item="brand" open="("
                 close=")">
            #{brand}
        </foreach>
        WHERE t.location = #{location}
        and t.status in ('READY_WAVE_GENERATED','READY_PICK','PICKING','READY_OUTBOUND','OUTBOUNDING','READY_DELIVERY')
        and t.workspace = #{workspace}
        and t.picking_type = #{pickingType}
        and t.stockout_type != 'OVERSEA_DELIVERY'
        <if test="workspace == 'B2C_BAG_AREA'">
            and t.store_id != 8886
        </if>
        <if test="request.areaName != null and request.areaName !=''">
            and t.area_name = #{request.areaName}
        </if>
        <if test="request!=null and request.stockoutType != null and request.stockoutType !=''">
            and t.stockout_type = #{request.stockoutType}
        </if>
        <if test="request!=null and request.spaceId != null">
            and t.space_id = #{request.spaceId}
        </if>
        <if test="request!=null and request.logisticsCompany != null and request.logisticsCompany !=''">
            and t.logistics_company =#{request.logisticsCompany}
        </if>
        <if test="request!=null and request.logisticsLabelSize != null and request.logisticsLabelSize !=''">
            and t.logistics_label_size =#{request.logisticsLabelSize}
        </if>
        <if test="request!=null and request.businessType != null and request.businessType !=''">
            and t.business_type =#{request.businessType}
        </if>
        <if test="request!=null and request.isUrgent != null">
            and t.is_urgent = #{request.isUrgent}
        </if>
        <if test="request!=null and request.isNeedProcess != null">
            and t.is_need_process = #{request.isNeedProcess}
        </if>
        <if test="request!=null and request.multipleSpace != null">
            and t.multiple_space = #{request.multipleSpace}
        </if>
        <if test="request.tagName != null and request.tagName !=''">
            and m.tag_name = #{request.tagName}
        </if>

        GROUP BY t.logistics_company,m.tag_name
        ORDER BY t.logistics_company desc
    </select>

    <select id="findSmallBagStockoutIdsGenerateBatchList"
            resultType="com.nsy.api.wms.domain.stockout.StockoutGenerateBatchId">
        select t.logistics_company as logisticsCompany,
        m.tag_name as tagName,
        t.stockout_order_id as stockoutOrderId
        FROM stockout_order t
        left join bd_tag_mapping m on m.reference_no = t.stockout_order_no
        and m.reference_type = 'ORDER'
        and m.tag_name in
        <foreach collection="request.brandList" separator="," index="index" item="brand" open="("
                 close=")">
            #{brand}
        </foreach>
        WHERE t.location = #{location}
        and t.status = 'READY_WAVE_GENERATED'
        and t.workspace = #{workspace}
        and t.picking_type = #{pickingType}
        and t.stockout_type != 'OVERSEA_DELIVERY'
        <if test="workspace == 'B2C_BAG_AREA'">
            and t.store_id != 8886
        </if>
        <if test="request.areaName != null and request.areaName !=''">
            and t.area_name = #{request.areaName}
        </if>
        <if test="request!=null and request.stockoutType != null and request.stockoutType !=''">
            and t.stockout_type = #{request.stockoutType}
        </if>
        <if test="request!=null and request.spaceId != null">
            and t.space_id = #{request.spaceId}
        </if>
        <if test="request!=null and request.logisticsCompany != null and request.logisticsCompany !=''">
            and t.logistics_company =#{request.logisticsCompany}
        </if>
        <if test="request!=null and request.logisticsLabelSize != null and request.logisticsLabelSize !=''">
            and t.logistics_label_size =#{request.logisticsLabelSize}
        </if>
        <if test="request!=null and request.businessType != null and request.businessType !=''">
            and t.business_type =#{request.businessType}
        </if>
        <if test="request!=null and request.isUrgent != null">
            and t.is_urgent = #{request.isUrgent}
        </if>
        <if test="request!=null and request.isNeedProcess != null">
            and t.is_need_process = #{request.isNeedProcess}
        </if>
        <if test="request!=null and request.multipleSpace != null">
            and t.multiple_space = #{request.multipleSpace}
        </if>
        <if test="request.tagName != null and request.tagName !=''">
            and m.tag_name = #{request.tagName}
        </if>
    </select>

    <select id="findStockoutGenerateBatchHotList"
            resultType="com.nsy.api.wms.response.stockout.StockoutGenerateBatchHotResponse">
        select s.sku,t.logistics_company as logisticsCompany,
        GROUP_CONCAT(DISTINCT(t.stockout_order_id)) stockoutOrderIdStr,
        sum(t.status = 'READY_WAVE_GENERATED') as readyGeneratedBatchQty
        from stockout_order_item s
        LEFT JOIN stockout_order t
        on t.stockout_order_id = s.stockout_order_id
        RIGHT JOIN
        (select sku,count(sku) qty from
        (select s.sku
        from stockout_order_item s
        LEFT JOIN stockout_order t
        on t.stockout_order_id = s.stockout_order_id
        WHERE t.location = #{location}
        and t.workspace = 'B2C_BAG_AREA'
        and t.status = 'READY_WAVE_GENERATED'
        and t.picking_type = 'FIND_DOC_BY_GOODS'
        and t.stockout_type != 'OVERSEA_DELIVERY'
        <if test="request!=null and request.stockoutType != null and request.stockoutType !=''">
            and t.stockout_type = #{request.stockoutType}
        </if>
        <if test="request!=null and request.spaceId != null and request.spaceId !=''">
            and t.space_id = #{request.spaceId}
        </if>
        <if test="request.areaName != null and request.areaName !=''">
            and t.area_name = #{request.areaName}
        </if>
        <if test="request!=null and request.logisticsCompany != null and request.logisticsCompany !=''">
            and t.logistics_company =#{request.logisticsCompany}
        </if>
        <if test="request!=null and request.sku != null and request.sku !=''">
            and s.sku =#{request.sku}
        </if>
        ) t
        GROUP BY t.sku
        HAVING qty > 30) t1
        on t1.sku = s.sku
        GROUP by t.logistics_company,s.sku
        ORDER BY t.logistics_company desc
    </select>

    <select id="findFindGoodsByDoc" resultType="com.nsy.api.wms.response.stockout.StockoutGenerateBatchByDocResponse">
        select t.logistics_company as logisticsCompany,m.tag_name as tagName,
        s.sku,s.space_area_name as spaceAreaName,
        GROUP_CONCAT(DISTINCT(case t.status when 'READY_WAVE_GENERATED' then t.stockout_order_id else null end))
        stockoutOrderIdStr,
        sum(t.status = 'READY_WAVE_GENERATED') as readyGeneratedBatchQty,
        sum(t.status in ('READY_PICK','PICKING','READY_OUTBOUND','OUTBOUNDING','READY_DELIVERY')) as inTransitBatchQty
        from stockout_order_item s
        LEFT JOIN stockout_order t
        on t.stockout_order_id = s.stockout_order_id
        left join bd_tag_mapping m on m.reference_no = t.stockout_order_no
        and m.reference_type = 'ORDER'
        and m.tag_name in
        <foreach collection="request.brandList" separator="," index="index" item="brand" open="("
                 close=")">
            #{brand}
        </foreach>
        WHERE t.location = #{location}
        and t.status in ('READY_WAVE_GENERATED','READY_PICK','PICKING','READY_OUTBOUND','OUTBOUNDING','READY_DELIVERY')
        and t.workspace = #{workspace}
        and t.picking_type = 'FIND_GOODS_BY_DOC'
        and t.stockout_type != 'OVERSEA_DELIVERY'
        <if test="request!=null and request.stockoutType != null and request.stockoutType !=''">
            and t.stockout_type = #{request.stockoutType}
        </if>
        <if test="request!=null and request.spaceId != null">
            and t.space_id = #{request.spaceId}
        </if>
        <if test="request.areaName != null and request.areaName !=''">
            and t.area_name = #{request.areaName}
        </if>
        <if test="request!=null and request.logisticsCompany != null and request.logisticsCompany !=''">
            and t.logistics_company =#{request.logisticsCompany}
        </if>
        <if test="request!=null and request.logisticsLabelSize != null and request.logisticsLabelSize !=''">
            and t.logistics_label_size =#{request.logisticsLabelSize}
        </if>
        <if test="request!=null and request.sku != null and request.sku !=''">
            and s.sku =#{request.sku}
        </if>
        <if test="request!=null and request.spaceAreaName != null and request.spaceAreaName !=''">
            and s.space_area_name =#{request.spaceAreaName}
        </if>
        <if test="request!=null and request.isNeedProcess != null">
            and t.is_need_process = #{request.isNeedProcess}
        </if>
        <if test="request!=null and request.multipleSpace != null">
            and t.multiple_space = #{request.multipleSpace}
        </if>
        <if test="request.tagName != null and request.tagName !=''">
            and m.tag_name = #{request.tagName}
        </if>
        GROUP by t.logistics_company,m.tag_name,s.sku,s.space_area_name
        ORDER BY t.logistics_company desc
    </select>

    <select id="findWholePick" resultType="com.nsy.api.wms.domain.stockout.StockoutGenerateBatchWhole">
        select logistics_company as logisticsCompany,
        GROUP_CONCAT(DISTINCT(case t.status when 'READY_WAVE_GENERATED' then t.stockout_order_id else null end))
        stockoutOrderIdStr,
        sum(t.is_urgent = 1) as readyGeneratedBatchUrgentQty,
        sum(t.is_urgent = 0) as readyGeneratedBatchQty,
        group_concat((select group_concat(s.space_area_name) from stockout_order_item s where t.stockout_order_id =
        s.stockout_order_id)) as spaceAreaStr,
        sum((select sum(s.qty) from stockout_order_item s where t.stockout_order_id =
        s.stockout_order_id)) as skuQty
        FROM stockout_order t
        WHERE t.location = #{location}
        and t.workspace = #{workspace}
        and t.picking_type = 'WHOLE_PICK'
        and t.status = 'READY_WAVE_GENERATED'
        and t.stockout_type != 'OVERSEA_DELIVERY'
        <if test="request!=null and request.stockoutType != null and request.stockoutType !=''">
            and t.stockout_type = #{request.stockoutType}
        </if>
        <if test="request!=null and request.spaceId != null">
            and t.space_id = #{request.spaceId}
        </if>
        <if test="request.areaName != null and request.areaName !=''">
            and t.area_name = #{request.areaName}
        </if>
        <if test="request!=null and request.logisticsCompany != null and request.logisticsCompany !=''">
            and t.logistics_company =#{request.logisticsCompany}
        </if>
        <if test="request!=null and request.businessType != null and request.businessType !=''">
            and t.business_type =#{request.businessType}
        </if>
        <if test="request!=null and request.isUrgent != null">
            and t.is_urgent = #{request.isUrgent}
        </if>
        <if test="request!=null and request.isNeedProcess != null">
            and t.is_need_process = #{request.isNeedProcess}
        </if>
        <if test="request!=null and request.multipleSpace != null">
            and t.multiple_space = #{request.multipleSpace}
        </if>
        GROUP BY t.logistics_company
        ORDER BY logistics_company desc
    </select>

    <select id="findSecondSortList"
            resultType="com.nsy.api.wms.domain.stockout.StockoutGenerateBatch">
        select t.stockout_order_id as stockoutOrderId,sum(s.qty) skuQty,
        t.status
        from stockout_order_item s
        LEFT JOIN stockout_order t
        on t.stockout_order_id = s.stockout_order_id
        WHERE t.location = #{location}
        and t.workspace = #{workspace}
        and t.picking_type = 'SECOND_SORT'
        and t.status = 'READY_WAVE_GENERATED'
        and t.stockout_type != 'OVERSEA_DELIVERY'
        and t.platform_name != 'PinDuoDuo'
        <if test="request!=null and request.stockoutType != null and request.stockoutType !=''">
            and t.stockout_type = #{request.stockoutType}
        </if>
        <if test="request!=null and request.spaceId != null">
            and t.space_id = #{request.spaceId}
        </if>
        <if test="request!=null and request.businessType != null and request.businessType !=''">
            and t.business_type =#{request.businessType}
        </if>
        <if test="request!=null and request.isUrgent != null">
            and t.is_urgent = #{request.isUrgent}
        </if>
        <if test="request!=null and request.isNeedProcess != null">
            and t.is_need_process = #{request.isNeedProcess}
        </if>
        <if test="request!=null and request.multipleSpace != null">
            and t.multiple_space = #{request.multipleSpace}
        </if>
        <if test="request!=null and request.addVas != null and request.addVas == 1">
            and s.`vas_type` != ''
        </if>
        <if test="request!=null and request.addVas != null and request.addVas == 0">
            and s.`vas_type` = ''
        </if>
        GROUP by t.stockout_order_id
    </select>

    <select id="findWholePickList"
            resultType="com.nsy.api.wms.domain.stockout.StockoutGenerateBatch">
        select t.stockout_order_id as stockoutOrderId,t.is_urgent isUrgent,
        t.status,group_concat(sku) as skuStr,
        group_concat(space_area_name) as spaceAreaStr,
        sum(s.qty) as skuQty
        from stockout_order_item s
        LEFT JOIN stockout_order t
        on t.stockout_order_id = s.stockout_order_id
        WHERE t.location = #{location}
        and t.status = 'READY_WAVE_GENERATED'
        and t.workspace = #{workspace}
        and t.picking_type = 'WHOLE_PICK'
        and t.stockout_type != 'OVERSEA_DELIVERY'
        <if test="request!=null and request.stockoutType != null and request.stockoutType !=''">
            and t.stockout_type = #{request.stockoutType}
        </if>
        <if test="request!=null and request.spaceId != null">
            and t.space_id = #{request.spaceId}
        </if>
        <if test="request!=null and request.businessType != null and request.businessType !=''">
            and t.business_type =#{request.businessType}
        </if>
        <if test="request!=null and request.isNeedProcess != null">
            and t.is_need_process = #{request.isNeedProcess}
        </if>
        <if test="request!=null and request.multipleSpace != null">
            and t.multiple_space = #{request.multipleSpace}
        </if>
        GROUP by t.stockout_order_id
        order by t.stockout_order_id
    </select>

    <select id="findReturnProductList"
            resultType="com.nsy.api.wms.domain.stockout.StockoutOrderReturnProductList">
        SELECT
        o.stockout_order_id,
        o.stockout_order_no,
        o.store_id,
        o.store_name,
        o.logistics_no,
        o.logistics_company,
        outOrderItem.stockout_order_item_id as orderItemId,
        specInfo.product_id,
        specInfo.spec_id,
        specInfo.image_url as imageUrl,
        specInfo.thumbnail_image_url as thumbnailImageUrl,
        specInfo.preview_image_url as previewImageUrl,
        specInfo.color as color,
        specInfo.size as size,
        outOrderItem.order_no,
        outOrderItem.sku as sku,
        outOrderItem.barcode as barcode,
        outOrderItem.space_area_name as spaceAreaName,
        outOrderItem.qty as qty,
        outOrderItem.scan_qty as scanQty,
        outOrderItem.shipment_qty as shipmentQty
        FROM
        stockout_order o
        left join
        stockout_order_item outOrderItem on o.stockout_order_id = outOrderItem.stockout_order_id
        LEFT JOIN product_spec_info specInfo on specInfo.spec_id = outOrderItem.spec_id
        <where>
            <if test="scanNumber != null and scanNumber !=''">
                o.logistics_no = #{scanNumber} or outOrderItem.order_no = #{scanNumber}
            </if>
        </where>
    </select>

    <select id="selectBatchDetailList" resultType="com.nsy.api.wms.response.stockout.StockoutBatchOrderResponse">
        select
        tb.batch_id as batchId,t.stockout_order_no as stockoutOrderNo,ti.order_no as orderNo,
        sum(ti.qty) as skuQty,sum(ti.scan_qty) as scanQty,(case when sum(t.is_lack) >0 then 1 ELSE 0 end) as isLack
        from stockout_order_item ti
        LEFT JOIN
        stockout_order t
        on t.stockout_order_id = ti.stockout_order_id
        LEFT JOIN
        stockout_batch_order tb
        ON ti.stockout_order_id = tb.stockout_order_id
        where tb.batch_id in
        <foreach collection="batchIds" separator="," index="index" item="batchId" open="("
                 close=")">
            #{batchId}
        </foreach>
        <if test="request.stockoutOrderNo != null and request.stockoutOrderNo !=''">
            and t.stockout_order_no =#{request.stockoutOrderNo}
        </if>
        <if test="request.orderNo != null and request.orderNo !=''">
            and ti.order_no =#{request.orderNo}
        </if>
        GROUP BY tb.batch_id,t.stockout_order_no,ti.order_no
    </select>

    <select id="selectIds" resultType="java.lang.Integer">
        SELECT stockout_order_id
        FROM
        stockout_order
        <where>
            <if test="logisticsCompanyList != null and logisticsCompanyList.size()>0">
                and logistics_company IN
                <foreach collection="logisticsCompanyList" separator="," index="index" item="logisticsCompany" open="("
                         close=")">
                    #{logisticsCompany}
                </foreach>
            </if>
            <if test="logisticsNoList != null and logisticsNoList.size()>0">
                and logistics_no IN
                <foreach collection="logisticsNoList" separator="," index="index" item="logisticsNo" open="("
                         close=")">
                    #{logisticsNo}
                </foreach>
            </if>
        </where>
    </select>


    <select id="findFindGoodsByDocPrint"
            resultType="com.nsy.api.wms.response.stockout.StockoutGenerateBatchByDocPrintResponse">
        SELECT t.batch_id batchId,
        t.logistics_company logisticsCompany,
        count(DISTINCT o.stockout_order_id) stockoutOrderQty,
        GROUP_CONCAT(DISTINCT(i.sku)) sku,
        case sum(IFNULL(l.is_print,0)) when 0 then '未打印' else '已打印' end printStatus,
        t.logistics_label_size logisticsLabelSize,
        m.tag_name as tagName
        FROM stockout_batch t
        LEFT JOIN stockout_batch_order o
        on o.batch_id = t.batch_id
        LEFT JOIN stockout_order so
        on o.stockout_order_id = so.stockout_order_id
        left join bd_tag_mapping m on m.reference_no = so.stockout_order_no
        and m.reference_type = 'ORDER'
        and m.tag_name in
        <foreach collection="request.brandList" separator="," index="index" item="brand" open="("
                 close=")">
            #{brand}
        </foreach>
        LEFT JOIN stockout_order_label l
        on o.stockout_order_id = l.stockout_order_id
        LEFT JOIN stockout_batch_order_item i
        on o.batch_order_id = i.batch_order_id
        WHERE t.workspace = 'B2C_BAG_AREA'
        and t.picking_type = 'FIND_GOODS_BY_DOC'
        and t.batch_type != 'LACK_WAVE'
        AND t.status NOT IN ('COMPLETED','CANCELLED')
        <if test="request!=null and request.batchId != null">
            and t.batch_Id = #{request.batchId}
        </if>
        <if test="request!=null and request.spaceId != null">
            and t.space_id = #{request.spaceId}
        </if>
        <if test="request.areaName != null and request.areaName !=''">
            and so.area_name = #{request.areaName}
        </if>
        <if test="request!=null and request.logisticsCompany != null and request.logisticsCompany !=''">
            and t.logistics_company =#{request.logisticsCompany}
        </if>
        <if test="request!=null and request.logisticsLabelSize != null and request.logisticsLabelSize !=''">
            and t.logistics_label_size =#{request.logisticsLabelSize}
        </if>
        <if test="request!=null and request.sku != null and request.sku !=''">
            and i.sku =#{request.sku}
        </if>
        <if test="request!=null and request.isPrint != null">
            and IFNULL(l.is_print,0) =#{request.isPrint}
        </if>
        <if test="request!=null and request.isNeedProcess != null">
            and t.is_need_process = #{request.isNeedProcess}
        </if>
        <if test="request!=null and request.multipleSpace != null">
            and t.multiple_space = #{request.multipleSpace}
        </if>
        <if test="request.tagName != null and request.tagName !=''">
            and m.tag_name = #{request.tagName}
        </if>
        group by t.batch_id
        order by t.batch_id
    </select>

    <select id="selectByPickingTypeListAndStatusList"
            resultType="java.lang.String">
        SELECT
        GROUP_CONCAT(DISTINCT(outOrder.stockout_order_id) ) AS stockoutOrderIdStr
        FROM
        stockout_order outOrder
        left join bd_tag_mapping btm on outOrder.stockout_order_no = btm.reference_no
        and btm.tag_name in
        <foreach collection="brandList" separator="," index="index" item="brand" open="("
                 close=")">
            #{brand}
        </foreach>
        inner JOIN stockout_order_item item ON item.stockout_order_id = outOrder.stockout_order_id
        <where>
            <choose>
                <when test="brandName != null and brandName !=''">
                    and btm.tag_name = #{brandName}
                </when>
                <otherwise>
                    and btm.id is null
                </otherwise>
            </choose>

            <if test="statusList!=null and statusList.size()>0">
                and outOrder.status in
                <foreach collection="statusList" separator="," index="index" item="status" open="("
                         close=")">
                    #{status}
                </foreach>
            </if>
            <if test="pickingTypeList!=null and pickingTypeList.size()>0">
                and outOrder.picking_type in
                <foreach collection="pickingTypeList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY outOrder.logistics_company,item.sku
    </select>

    <select id="selectLackList" resultType="com.nsy.api.wms.response.stockout.StockoutOrderLackResponse">
        SELECT
        a.stockoutOrderNo,
        a.sku,
        a.businessType,
        a.limitQty,
        ifnull( li.lack_qty, 0 ) AS lackQty
        FROM
        (
        SELECT
        t.stockout_order_no AS stockoutOrderNo,
        li.sku AS sku,
        max( o.business_type ) AS businessType,
        ifnull( max( dr.rule_value ), 0 ) AS limitQty,
        max( li.stockout_order_lack_item_id ) AS lackItemId
        FROM
        stockout_order_lack t
        LEFT JOIN stockout_order_lack_item li ON li.stockout_order_lack_id = t.stockout_order_lack_id
        LEFT JOIN stockout_order o ON o.stockout_order_no = t.stockout_order_no
        LEFT JOIN bd_lack_delivery_rule dr ON dr.business_type = o.business_type
        WHERE
        t.stockout_order_no in
        <foreach collection="stockoutOrderNos" separator="," index="index" item="stockoutOrderNo" open="("
                 close=")">
            #{stockoutOrderNo}
        </foreach>
        GROUP BY
        t.stockout_order_no,
        li.sku
        ) a
        LEFT JOIN stockout_order_lack_item li ON li.stockout_order_lack_item_id = a.lackItemId
    </select>
    <select id="findTopByOrderNo" resultType="com.nsy.wms.repository.entity.stockout.StockoutOrderEntity">
        SELECT outOrder.*
        FROM stockout_order outOrder
                 LEFT JOIN stockout_order_item item ON item.stockout_order_id = outOrder.stockout_order_id
        where item.order_no = #{orderNo}
        order by outOrder.stockout_order_id desc
        limit 1
    </select>
    <select id="findTopByOrderNoWithoutLocation"
            resultType="com.nsy.wms.repository.entity.stockout.StockoutOrderEntity">
        SELECT outOrder.*
        FROM stockout_order outOrder
                 LEFT JOIN stockout_order_item item ON item.stockout_order_id = outOrder.stockout_order_id
        where item.order_no = #{orderNo}
        order by outOrder.stockout_order_id desc
        limit 1
    </select>
    <select id="findPddSecondSortList" resultType="com.nsy.api.wms.domain.stockout.StockoutGenerateBatch">
        select t.stockout_order_id as stockoutOrderId,sum(s.qty) skuQty,
        t.status
        from stockout_order_item s
        LEFT JOIN stockout_order t
        on t.stockout_order_id = s.stockout_order_id
        WHERE t.location = #{location}
        and t.workspace = #{workspace}
        and t.picking_type = 'SECOND_SORT'
        and t.status = 'READY_WAVE_GENERATED'
        and t.stockout_type != 'OVERSEA_DELIVERY'
        and t.platform_name = 'PinDuoDuo'
        <if test="request!=null and request.stockoutType != null and request.stockoutType !=''">
            and t.stockout_type = #{request.stockoutType}
        </if>
        <if test="request!=null and request.spaceId != null">
            and t.space_id = #{request.spaceId}
        </if>
        <if test="request!=null and request.businessType != null and request.businessType !=''">
            and t.business_type =#{request.businessType}
        </if>
        <if test="request!=null and request.isUrgent != null">
            and t.is_urgent = #{request.isUrgent}
        </if>
        <if test="request!=null and request.isNeedProcess != null">
            and t.is_need_process = #{request.isNeedProcess}
        </if>
        <if test="request!=null and request.multipleSpace != null">
            and t.multiple_space = #{request.multipleSpace}
        </if>
        GROUP by t.stockout_order_id
    </select>
    <select id="findBatchOrderPlatform" resultType="java.lang.String">
        select DISTINCT(so.platform_name)
        from stockout_batch_order sbo
                 LEFT JOIN stockout_order so on so.stockout_order_id = sbo.stockout_order_id
        where sbo.batch_id = #{batchId}
    </select>
    <select id="findReadyToGetTransparencyCode"
            resultType="java.lang.Integer">
        SELECT DISTINCT(stockout_order.stockout_order_id)
        FROM stockout_order stockout_order
                 LEFT JOIN stockout_order_item stockout_order_item
                           ON stockout_order.stockout_order_id = stockout_order_item.stockout_order_id
        WHERE stockout_order.`status` = 'READY'
          AND stockout_order_item.is_transparency = 1
    </select>
    <select id="getDeliverDateIsNull" resultType="com.nsy.wms.repository.entity.stockout.StockoutOrderEntity">
        select o.stockout_order_id  as stockoutOrderId,
               max(s.delivery_date) as deliveryDate
        from stockout_order o
                 INNER JOIN stockout_shipment_item si
                            on si.stockout_order_no = o.stockout_order_no
                                and si.is_deleted = 0
                 INNER JOIN stockout_shipment s
                            on s.shipment_id = si.shipment_id
                                and s.`status` = 'SHIPPED'
        where o.`status` = 'DELIVERED'
          and o.stockout_order_id BETWEEN #{from} and #{to}
          and o.delivery_date is null
        GROUP BY o.stockout_order_id
        having deliveryDate is not null
    </select>
    <select id="isCreateFbaReplenishOrder" resultType="java.lang.Boolean">
        select ifnull(max(if(s.replenish_order_status = 'DEAL', 1, 0)), 0)
        from stockout_shipment_item si
                 inner join stockout_shipment s
                            on s.shipment_id = si.shipment_id
        where si.stockout_order_no = #{stockoutOrderNo}
          and si.is_deleted = 0
    </select>
    <select id="countUnFullPrematchOrder" resultType="java.lang.Integer">
        select count(*)
        from stockout_order o
        where status = 'UN_FULL_PRE_MATCH'
          and o.update_date &lt; #{afterDate}
    </select>
    <select id="findPrematchInfoByStockOutId" resultType="com.nsy.api.wms.domain.stockout.StockoutOrderPrematchInfo">
        SELECT stockout_order.stockout_order_no,
               stockout_order.stockout_order_id,
               stockout_order.status,
               GROUP_CONCAT(DISTINCT (stockout_order_item.order_no)) as orderNo
        FROM stockout_order stockout_order
                 LEFT JOIN stockout_order_item stockout_order_item
                           ON stockout_order.stockout_order_id = stockout_order_item.stockout_order_id
        WHERE stockout_order.stockout_order_id = #{stockoutOrderId}
        group by stockout_order.stockout_order_id
    </select>
    <select id="isAllDeliverdByReplenishOrder" resultType="java.lang.Boolean">
        select ifnull(min(if(o.status = 'DELIVERED', 1, 0)), 0)
        from stockout_order o
        where o.replenish_order = #{replenishOrder}
          and o.status != 'CANCELLED'
    </select>
    <select id="listByShipmentIdList" resultType="com.nsy.wms.repository.entity.stockout.StockoutOrderEntity">
        select distinct o.* from
        stockout_shipment_item si
        inner join stockout_order o
        on si.stockout_order_no = o.stockout_order_no
        where si.is_deleted = 0
        and si.shipment_id in
        <foreach collection="shipmentIdList" separator="," index="index" item="shipmentId" open="("
                 close=")">
            #{shipmentId}
        </foreach>
    </select>
    <select id="findTopByOrderNoOrShipmentId"
            resultType="com.nsy.wms.repository.entity.stockout.StockoutOrderEntity">
        SELECT outOrder.*
        FROM stockout_order outOrder
        LEFT JOIN stockout_order_item item ON item.stockout_order_id = outOrder.stockout_order_id
        where item.order_no = #{orderNo} or item.order_no = #{shipmentId}
        order by outOrder.stockout_order_id desc
        limit 1
    </select>
    <select id="isApplyFbaLabel" resultType="java.lang.Boolean">
        select ifnull(max(if(s.fba_label_status in ('APPLYING', 'COMPLETE', 'EXCEPTION'), 1, 0)), 0)
        from stockout_shipment_item si
                 inner join stockout_shipment s
                            on s.shipment_id = si.shipment_id
        where si.stockout_order_no = #{stockoutOrderNo}
          and si.is_deleted = 0
    </select>
    <select id="getStockoutIdByReplenishOrder" resultType="java.lang.Integer">
        select stockout_order_id from stockout_order where replenish_order = #{replenishOrder}
    </select>
    <select id="isReplenishOrderWaitDelivery" resultType="java.lang.Boolean">
        select count(stockout_order_id) = count(if(`status` = 'READY_DELIVERY',stockout_order_id,null))
        from stockout_order
        where replenish_order = #{replenishOrder}
        and status not in ('CANCELLING','CANCELLED')
    </select>

    <select id="exportFbaReport" resultType="com.nsy.api.wms.domain.stockout.StockoutFbaReportExport">
        select
            so.logistics_company  as logisticsCompany,
            so.store_name as storeName,
            ss.logistics_company as logisticsCompanyActual,
            ss.replenish_order as replenishOrder,
            ss.fba_replenish_type as fbaReplenishType,
            ssi.order_no as orderNo,
            sum(ssi.qty) as qty,
            ss.delivery_date as deliveryDate
        from
            nsy_wms.stockout_shipment ss
                left join nsy_wms.stockout_shipment_item ssi on ss.shipment_id = ssi.shipment_id
                left join nsy_wms.stockout_order so on so.stockout_order_no =ssi.stockout_order_no
        where
            ss.location ='QUANZHOU'
          and
            so.stockout_type ='FIRST_LEG_DELIVERY'
          and
            so.platform_name ='amazon'
          and so.`status` = 'DELIVERED'
          and
            so.delivery_date between #{startDate} and #{endDate}
        group by so.stockout_order_no
    </select>
    
    <select id="getStockoutOrderQty" resultType="java.lang.Integer">
        select sum(oi.qty) from stockout_order o 
        inner join stockout_order_item oi on oi.stockout_order_id = o.stockout_order_id
        where stockout_order_no in
        <foreach collection="stockoutOrderNoList" separator="," index="index" item="stockoutOrderNo" open="("
                 close=")">
            #{stockoutOrderNo}
        </foreach>
    </select>

    <select id="findNoDeliveryOrderDetails" resultType="java.util.Map">
        SELECT 
            so.stockout_order_no AS stockoutOrderNo,
            GROUP_CONCAT(DISTINCT soi.order_no) AS orderNo,
            so.store_name AS storeName,
            so.status AS status,
            so.logistics_company AS logisticsCompany,
            so.logistics_no AS logisticsNo,
            so.create_date AS createDate,
            so.latest_delivery_date AS latestDeliveryDate
        FROM 
            stockout_order so
        LEFT JOIN 
            stockout_order_item soi ON so.stockout_order_id = soi.stockout_order_id
        WHERE 
            so.status IN 
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
            <if test="createStartDate != null">
                AND so.create_date >= #{createStartDate}
            </if>
            <if test="createEndDate != null">
                AND so.create_date &lt;= #{createEndDate}
            </if>
        GROUP BY 
            so.stockout_order_id
        ORDER BY 
            so.create_date DESC
    </select>

    <select id="getStoreIdByPlatformNameAndCreateDate" resultType="java.lang.Integer">
        SELECT DISTINCT
            so.store_id
        FROM
            stockout_order so
        WHERE
            so.platform_name = #{platformName}
            AND so.create_date &gt; #{startDate}
            AND so.create_date &lt; now()
    </select>
</mapper>
